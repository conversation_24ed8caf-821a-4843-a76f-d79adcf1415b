# Command Management Module

## Overview

The Command Management module is the core order processing system of JocelEpress, handling customer orders from initial recording through final delivery. It implements a sophisticated workflow management system with multi-stage tracking and comprehensive product management.

## Business Purpose

- **Primary Function**: Manage customer orders throughout their complete lifecycle
- **Business Value**: Provides complete visibility and control over order fulfillment process
- **User Benefits**: Streamlined order processing, automated workflow tracking, comprehensive customer management

## Data Models

### Command Entity
```kotlin
data class Command(
    val id: String = "",
    val client: ClientData = ClientData(),
    val products: List<MiniProduct> = listOf(),
    val commandStepIndex: Int = 0,
    val observation: List<String> = listOf(),
    val paymentProofImageUrl: String? = null,
    val proofUploaded: Boolean = false,
    val created: Date = Calendar.getInstance().time
)
```

### ClientData Entity
```kotlin
data class ClientData(
    val name: String = "",
    val tel: String = "",
    val country: String = "",
    val city: String = ""
)
```

### MiniProduct Entity
```kotlin
data class MiniProduct(
    val name: String = "",
    val quantity: Int = 0,
    val unitSellingPrice: Int = 0,
    val unitBuyingPrice: Int = 0,
    val description: String = "",
    val productEvolutionStep: String? = CommandStep.RECORD.step,
    val productImage: String? = null,
    val previewsPathName: String? = UUID.randomUUID().toString(),
    val soldOut: Boolean? = false
)
```

## Workflow Management

### Command Steps (6-Stage Process)

#### 1. RECORD (Enregistrement)
- **Purpose**: Initial order entry and customer data capture
- **Icon**: ic_record_24
- **Activities**:
  - Customer information collection
  - Product selection and specification
  - Initial pricing calculation
  - Order validation

#### 2. BUYING (Commander)
- **Purpose**: Product procurement and sourcing
- **Icon**: ic_buying_24
- **Activities**:
  - Supplier identification and contact
  - Product ordering from suppliers
  - Cost confirmation and adjustment
  - Procurement timeline establishment

#### 3. RECEIVED (Reçue)
- **Purpose**: Product receipt and quality verification
- **Icon**: ic_received_24
- **Activities**:
  - Product delivery confirmation
  - Quality inspection and verification
  - Inventory update
  - Damage assessment if applicable

#### 4. DELIVERED (Expédié)
- **Purpose**: Product shipment to destination
- **Icon**: ic_delivery_boat_24
- **Activities**:
  - Packaging and preparation
  - Shipping arrangement
  - Tracking number assignment
  - Customer notification

#### 5. READY (Prêt)
- **Purpose**: Product ready for customer pickup/delivery
- **Icon**: ic_home_24
- **Activities**:
  - Arrival confirmation at destination
  - Customer notification
  - Pickup scheduling
  - Final preparation

#### 6. OK (Livré)
- **Purpose**: Order completion and customer satisfaction
- **Icon**: ic_ok_24
- **Activities**:
  - Customer delivery confirmation
  - Payment collection
  - Order closure
  - Customer feedback collection

## User Interface Components

### CommandListScreen
- **Purpose**: Display and filter commands by status and date
- **Features**:
  - Step-based filtering (dropdown selection)
  - Month/year filtering
  - Pagination support
  - Search functionality
  - Real-time status updates

### CommandDetailsScreen
- **Purpose**: Comprehensive command management interface
- **Features**:
  - Complete command information display
  - Product management (add, edit, remove)
  - Client data editing
  - Payment proof upload
  - Status progression controls
  - Image gallery integration
  - Observation notes management

### Command Item Components
- **JACCommandItem**: List item display with key information
- **JACCommandOverview**: Summary statistics and quick actions
- **JACCurrentStepView**: Visual workflow progress indicator

## Key Features

### 1. Product Management
- **Add Products**: Integration with image catalog for product selection
- **Edit Products**: Modify quantities, prices, descriptions
- **Product Images**: Link products to catalog images
- **Pricing Management**: Separate buying and selling prices
- **Stock Status**: Track product availability and sold-out status

### 2. Client Management
- **Customer Information**: Name, phone, country, city
- **Contact Management**: Direct communication capabilities
- **Location Tracking**: Country and city-based organization
- **Customer History**: Previous orders and interactions

### 3. Financial Integration
- **Pricing Calculation**: Automatic profit margin calculation
- **Payment Tracking**: Payment proof image upload
- **Transaction Linking**: Connect to financial transaction records
- **Cost Analysis**: Buying vs selling price analysis

### 4. Workflow Automation
- **Status Progression**: Guided step-by-step advancement
- **Validation Rules**: Ensure proper workflow completion
- **Notification System**: Status change notifications
- **Progress Tracking**: Visual workflow indicators

### 5. Documentation & Proof
- **Payment Proof**: Image upload for payment verification
- **Observation Notes**: Detailed notes for each order
- **Image Gallery**: Product images and documentation
- **Audit Trail**: Complete change history (planned for rebuild)

### 6. Command Link Sharing (Customer Tracking)
- **Shareable URLs**: Generate secure, time-limited links for customer access
- **Customer Portal**: Dedicated customer view of order details and progress
- **Real-time Updates**: Live status updates visible to customers
- **Mobile Optimization**: Responsive design for customer mobile access
- **Security Controls**: Token-based access with expiration and revocation
- **Notification Integration**: Automatic link sharing via SMS/email

## User Interactions

### Creating New Commands
1. Navigate to Command screen
2. Click "Add Command" button
3. Enter customer information
4. Add products from catalog
5. Set pricing and quantities
6. Save initial command (RECORD status)

### Managing Existing Commands
1. Browse command list with filters
2. Select command for details
3. Update status through workflow steps
4. Add/modify products as needed
5. Upload payment proof when received
6. Add observation notes
7. Complete order workflow

### Filtering and Search
1. Filter by command step status
2. Filter by month and year
3. Search by customer name or details
4. Sort by creation date or status

## Business Rules

### Workflow Progression
- Commands must progress through steps sequentially
- Each step requires specific validation criteria
- Status changes are tracked with timestamps
- Rollback capabilities for error correction

### Product Management
- Products must have valid pricing information
- Quantities must be positive integers
- Product images are optional but recommended
- Sold-out status prevents further sales

### Financial Validation
- Selling price must be greater than buying price
- Payment proof required for completion
- Transaction records automatically generated
- Profit margins calculated and tracked

## Integration Points

### With Image Catalog
- Product selection from categorized images
- Image association with command products
- Category and genre filtering
- Image upload and management

### With Financial System
- Automatic transaction generation
- Profit/loss calculation
- Country-specific financial tracking
- Payment verification workflow

### With Cargo System
- Product shipping coordination
- Delivery status synchronization
- Logistics integration
- Tracking number management

## Performance Considerations

### Current Implementation
- LiveData for real-time updates
- Firestore pagination for large datasets
- Image caching for product photos
- Background data synchronization

### Optimization Opportunities (Rebuild)
- Server-side filtering and aggregation
- Optimized query patterns
- Reduced Firebase read operations
- Enhanced caching strategies
- Real-time collaboration features

## User Roles & Permissions

### Admin
- Full command management access
- Financial data visibility
- User activity monitoring
- System configuration

### Employee
- Command creation and editing
- Product management
- Customer interaction
- Status updates

### Deliverer
- Delivery status updates
- Customer confirmation
- Limited command information
- Mobile-optimized interface

## Command Link Sharing Feature

### Overview
The Command Link Sharing feature enables customers to track their orders through secure, shareable URLs without requiring account creation or app installation. This feature bridges the gap between the admin Android app and customer communication.

### Technical Implementation

#### Link Generation
```kotlin
data class CommandShareLink(
    val id: String = UUID.randomUUID().toString(),
    val commandId: String,
    val token: String = generateSecureToken(),
    val expiresAt: Timestamp,
    val accessCount: Int = 0,
    val maxAccess: Int = 50, // Configurable limit
    val isActive: Boolean = true,
    val createdBy: String,
    val created: Timestamp = Timestamp.now()
)

// Secure token generation
fun generateSecureToken(): String {
    return Base64.encodeToString(
        SecureRandom().generateSeed(32),
        Base64.URL_SAFE or Base64.NO_WRAP
    )
}

// Link generation service
interface CommandLinkService {
    suspend fun generateShareLink(
        commandId: String,
        expirationDays: Int = 30
    ): CommandShareLink

    suspend fun validateLink(token: String): CommandShareLink?
    suspend fun revokeLink(linkId: String)
    suspend fun refreshLink(linkId: String, newExpirationDays: Int): CommandShareLink
}
```

#### Security Model
```kotlin
// Access validation
class LinkAccessValidator {
    suspend fun validateAccess(token: String, clientIP: String): ValidationResult {
        val link = linkRepository.findByToken(token)

        return when {
            link == null -> ValidationResult.Invalid("Link not found")
            !link.isActive -> ValidationResult.Invalid("Link deactivated")
            link.expiresAt < Timestamp.now() -> ValidationResult.Expired
            link.accessCount >= link.maxAccess -> ValidationResult.RateLimited
            else -> {
                // Log access attempt
                auditService.logLinkAccess(link.id, clientIP)
                // Increment access count
                linkRepository.incrementAccessCount(link.id)
                ValidationResult.Valid(link)
            }
        }
    }
}

sealed class ValidationResult {
    data class Valid(val link: CommandShareLink) : ValidationResult()
    data class Invalid(val reason: String) : ValidationResult()
    object Expired : ValidationResult()
    object RateLimited : ValidationResult()
}
```

#### Customer View Data Model
```kotlin
// Filtered command data for customer view
data class CustomerCommandView(
    val id: String,
    val orderNumber: String, // Human-readable order number
    val status: CommandStep,
    val statusLabel: String,
    val statusDescription: String,
    val estimatedCompletion: Timestamp?,
    val customer: CustomerInfo,
    val products: List<CustomerProductView>,
    val timeline: List<StatusUpdate>,
    val totalAmount: BigDecimal,
    val currency: String,
    val lastUpdated: Timestamp
)

data class CustomerProductView(
    val name: String,
    val quantity: Int,
    val description: String,
    val status: String,
    val imageUrl: String? // Public-accessible image URL
)

data class StatusUpdate(
    val step: CommandStep,
    val timestamp: Timestamp,
    val description: String,
    val isCompleted: Boolean,
    val estimatedDate: Timestamp?
)
```

### User Experience Flow

#### Admin App - Link Generation
1. **Access Command Details**: Open command in admin app
2. **Generate Link**: Click "Share with Customer" button
3. **Configure Options**: Set expiration date and access limits
4. **Share Link**: Send via SMS, email, or copy to clipboard
5. **Monitor Access**: View link usage statistics and access logs

#### Customer Experience
1. **Receive Link**: Customer receives shareable URL via SMS/email
2. **Access Portal**: Click link to open customer tracking portal
3. **View Order**: See order details, products, and current status
4. **Track Progress**: Monitor real-time status updates
5. **Contact Support**: Access contact information if needed

### Link Sharing Integration Points

#### With Notification System
```kotlin
// Automatic link sharing
class CommandNotificationService {
    suspend fun sendStatusUpdate(commandId: String, newStatus: CommandStep) {
        val command = commandRepository.getById(commandId)
        val shareLink = linkService.getActiveLink(commandId)
            ?: linkService.generateShareLink(commandId)

        // Send SMS notification
        smsService.sendStatusUpdate(
            phoneNumber = command.client.tel,
            orderNumber = command.orderNumber,
            status = newStatus.label,
            trackingUrl = buildTrackingUrl(shareLink.token)
        )

        // Send email if available
        if (command.client.email.isNotEmpty()) {
            emailService.sendStatusUpdate(
                email = command.client.email,
                command = command,
                trackingUrl = buildTrackingUrl(shareLink.token)
            )
        }
    }
}
```

#### With Analytics System
```kotlin
// Link usage analytics
data class LinkAnalytics(
    val totalLinks: Int,
    val activeLinks: Int,
    val totalAccesses: Int,
    val uniqueVisitors: Int,
    val averageAccessesPerLink: Double,
    val mostAccessedCommands: List<CommandAccessStats>
)

interface LinkAnalyticsService {
    suspend fun getLinkAnalytics(dateRange: DateRange): LinkAnalytics
    suspend fun getCommandLinkStats(commandId: String): CommandLinkStats
    suspend fun getCustomerEngagement(): CustomerEngagementMetrics
}
```

## Enhanced Technical Implementation

### Repository Pattern
- `FirestoreRepository` for data access
- `CommandListLiveData` for real-time updates
- `CommandLinkRepository` for link management
- `CustomerViewRepository` for filtered customer data
- Pagination support with `FirestorePagingSource`
- Error handling and retry logic

### ViewModel Architecture
- `CommandDetailsScreenViewModel` for business logic
- `CommandLinkViewModel` for link management
- `CustomerTrackingViewModel` for customer portal
- State management with Compose
- Suspend functions for async operations
- Error handling and user feedback

### UI Components
- Material Design 3 components
- Custom JAC-prefixed components
- `JACShareLinkDialog` for link generation
- `JACLinkStatsView` for analytics display
- Responsive layout design
- Accessibility support

### Security Considerations
- **Token Security**: Cryptographically secure random tokens
- **Access Logging**: Complete audit trail of link access
- **Rate Limiting**: Configurable access limits per link
- **Expiration**: Automatic link expiration with configurable duration
- **Revocation**: Immediate link deactivation capability
- **IP Tracking**: Monitor access patterns for security

This Command Management module serves as the backbone of the JocelEpress business operations, providing comprehensive order management capabilities with sophisticated workflow automation, customer engagement through link sharing, and integration points throughout the application ecosystem.
