# Product Catalog & Image Management Module

## Overview

The Product Catalog & Image Management module provides a comprehensive digital catalog system for managing product images, categories, and metadata. It serves as the central repository for all product-related visual content with advanced filtering, search, and management capabilities.

## Business Purpose

- **Primary Function**: Manage and organize product images with categorization and metadata
- **Business Value**: Centralized product catalog with efficient search and retrieval
- **User Benefits**: Easy product discovery, organized catalog management, optimized image handling

## Data Models

### ImageData Entity
```kotlin
data class ImageData(
    val id: String = "",
    val url: String? = null,
    val thumbnailUrl: String? = null,  // Added for thumbnail support
    val category: String = "",
    val genre: String = "",
    val path: String? = null,
    val name: String? = null,
    val upload: Boolean = false,
    val width: Int? = null,           // Added for image dimensions
    val height: Int? = null,          // Added for image dimensions
    val size: Long? = null,           // Added for file size tracking
    val created: Date = Calendar.getInstance().time
)
```

### Product Entity (Legacy/Reference)
```kotlin
// Note: Currently commented out but shows intended structure
data class Product(
    val id: String,
    val name: String,
    val images: List<String>,
    val commandUrl: String,
    val category: String,
    val genre: String,
    val variants: String,
    val created: Date
)
```

## Image Management System

### Image Storage Architecture
- **Primary Storage**: Firebase Cloud Storage
- **Thumbnail Generation**: Automatic thumbnail creation for performance
- **Multiple Resolutions**: Support for different image sizes
- **Compression**: Optimized image compression for storage efficiency
- **CDN Integration**: Fast global image delivery

### Image Processing Pipeline
1. **Upload**: Original image upload to Firebase Storage
2. **Processing**: Automatic resizing and compression
3. **Thumbnail Generation**: Create optimized thumbnails
4. **Metadata Extraction**: Extract image dimensions and file size
5. **Database Storage**: Store metadata in Firestore
6. **Indexing**: Create searchable indexes for categories and genres

## Categorization System

### Category Management
- **Hierarchical Structure**: Organized category taxonomy
- **Dynamic Categories**: Add/remove categories as needed
- **Category Filtering**: Efficient category-based searches
- **Category Analytics**: Track category usage and popularity

### Genre Classification
- **Sub-categorization**: Genres within categories for detailed organization
- **Multi-genre Support**: Products can belong to multiple genres
- **Genre Filtering**: Combined category and genre filtering
- **Custom Genres**: User-defined genre creation

### Filtering Capabilities
- **Category Filters**: Filter by product categories
- **Genre Filters**: Filter by specific genres
- **Combined Filters**: Category + genre combinations
- **Search Integration**: Text-based search within filtered results
- **Real-time Filtering**: Instant filter application

## User Interface Components

### AddImageDataScreen
- **Purpose**: Main product catalog management interface
- **Features**:
  - Image upload and management
  - Category and genre assignment
  - Bulk image operations
  - Advanced filtering interface
  - Pagination for large catalogs

### EditImageDataScreen
- **Purpose**: Individual image editing and management
- **Features**:
  - Image metadata editing
  - Category and genre modification
  - Image replacement
  - Delete functionality
  - Preview capabilities

### Image Components
- **JACImage**: Optimized image display with caching
- **JACImagePicker**: Image selection interface
- **JACImageDataCreator**: Image upload and metadata entry
- **ImageDataItem**: Grid/list item display for catalog browsing

## Key Features

### 1. Image Upload & Management
- **Drag & Drop Upload**: Easy image upload interface
- **Batch Upload**: Multiple image upload support
- **Image Validation**: Format and size validation
- **Progress Tracking**: Upload progress indicators
- **Error Handling**: Robust error handling and retry logic

### 2. Advanced Search & Filtering
- **Category-based Search**: Filter by product categories
- **Genre-based Search**: Filter by specific genres
- **Text Search**: Search by image names and descriptions
- **Combined Filters**: Multiple filter combinations
- **Saved Searches**: Save frequently used filter combinations

### 3. Performance Optimization
- **Lazy Loading**: Load images on demand
- **Pagination**: Efficient data loading with pagination
- **Thumbnail Support**: Fast loading with thumbnail previews
- **Caching Strategy**: Multi-level caching for performance
- **Background Sync**: Background data synchronization

### 4. Image Processing
- **Automatic Compression**: Optimize images for storage
- **Thumbnail Generation**: Create multiple thumbnail sizes
- **Format Conversion**: Convert images to optimal formats
- **Dimension Tracking**: Track image dimensions and file sizes
- **Quality Control**: Maintain image quality standards

## Business Rules

### Image Management Rules
- Images must have valid category and genre assignments
- Image file size limits enforced for storage efficiency
- Supported formats: JPEG, PNG, WebP
- Thumbnail generation required for all uploaded images
- Metadata validation before storage

### Categorization Rules
- Categories must be predefined and validated
- Genres must belong to valid categories
- Category and genre names must be unique
- Hierarchical category structure maintained
- Case-insensitive category and genre handling

### Access Control Rules
- Role-based image management permissions
- Upload permissions based on user roles
- Edit permissions for image metadata
- Delete permissions restricted to authorized users
- Audit trail for all image operations

## User Interactions

### Adding New Images
1. Navigate to Add Image Data screen
2. Select images for upload
3. Assign category and genre
4. Add image metadata (name, description)
5. Upload and process images
6. Verify successful upload and categorization

### Managing Existing Images
1. Browse image catalog with filters
2. Select images for editing
3. Modify metadata and categorization
4. Update image information
5. Delete images if necessary

### Searching and Filtering
1. Apply category filters
2. Apply genre filters
3. Use text search for specific images
4. Combine multiple filters
5. Save frequently used filter combinations

## Integration Points

### With Command Management
- **Product Selection**: Choose images for command products
- **Product Association**: Link command products to catalog images
- **Image Display**: Show product images in command details
- **Catalog Integration**: Seamless product catalog browsing

### With Storage Systems
- **Firebase Storage**: Primary image storage backend
- **CDN Integration**: Fast global image delivery
- **Backup Systems**: Redundant image storage
- **Archive Management**: Long-term image archival

### With Performance Systems
- **Caching Layer**: Multi-level image caching
- **Compression Pipeline**: Automatic image optimization
- **Thumbnail Service**: Fast thumbnail generation
- **Background Processing**: Async image processing

## Performance Optimization

### Current Implementation
- **Pagination**: Efficient data loading with Paging 3
- **Image Caching**: Coil-based image caching with custom configuration
- **Thumbnail Support**: Automatic thumbnail generation and display
- **Background Sync**: Background data synchronization
- **Lazy Loading**: Load images on demand

### Advanced Optimizations (Rebuild)
- **Progressive Loading**: Load low-res then high-res images
- **Predictive Caching**: Cache likely-to-be-viewed images
- **Smart Compression**: AI-based image compression
- **Edge Caching**: Global CDN with edge caching
- **Real-time Sync**: Real-time catalog updates

## Technical Implementation

### Repository Pattern
- `ImageDataRepository` for data access and caching
- `ImageDataCacheManager` for local caching
- `StorageRepository` for file operations
- Pagination support with `FirestorePagingSource`

### ViewModel Architecture
- `AddImageDataScreenViewModel` for catalog management
- `EditImageDataViewModel` for image editing
- State management with Compose
- Async operations with coroutines

### Caching Strategy
- **Memory Cache**: In-memory image caching
- **Disk Cache**: Persistent local image cache
- **Database Cache**: Metadata caching in local database
- **Network Cache**: HTTP-based caching for remote images

## Image Processing Utilities

### ImageUtils Class
- **Compression**: Optimize image file sizes
- **Resizing**: Generate multiple image sizes
- **Format Conversion**: Convert between image formats
- **Thumbnail Generation**: Create optimized thumbnails
- **Metadata Extraction**: Extract image properties

### Processing Pipeline
1. **Validation**: Validate image format and size
2. **Compression**: Apply optimal compression settings
3. **Resizing**: Generate required image sizes
4. **Thumbnail Creation**: Create thumbnail versions
5. **Upload**: Store processed images
6. **Metadata Storage**: Save image metadata

## User Roles & Permissions

### Admin
- Full catalog management access
- Image upload and deletion permissions
- Category and genre management
- System configuration access
- Analytics and reporting access

### Employee
- Image upload and editing permissions
- Category assignment capabilities
- Basic catalog management
- Search and filter access
- Limited deletion permissions

### Deliverer
- Read-only catalog access
- Basic image viewing
- Simple search capabilities
- Mobile-optimized interface

## Analytics & Reporting

### Usage Analytics
- **Image View Statistics**: Track image popularity
- **Category Usage**: Monitor category performance
- **Search Analytics**: Analyze search patterns
- **Upload Statistics**: Track upload activity
- **Performance Metrics**: Monitor system performance

### Business Intelligence
- **Popular Products**: Identify trending products
- **Category Performance**: Analyze category effectiveness
- **User Behavior**: Understand catalog usage patterns
- **Storage Analytics**: Monitor storage usage and costs
- **Performance Insights**: Optimize catalog performance

## Advanced Features (Planned for Rebuild)

### AI-Powered Features
- **Auto-categorization**: AI-based category suggestion
- **Image Recognition**: Automatic product identification
- **Quality Assessment**: AI-based image quality scoring
- **Duplicate Detection**: Identify duplicate or similar images
- **Smart Tagging**: Automatic metadata generation

### Enhanced Search
- **Visual Search**: Search by image similarity
- **Advanced Filters**: Complex filter combinations
- **Faceted Search**: Multi-dimensional search interface
- **Personalized Results**: User-specific search results
- **Search Analytics**: Detailed search performance metrics

### Collaboration Features
- **Team Management**: Collaborative catalog management
- **Approval Workflows**: Image approval processes
- **Version Control**: Track image changes and versions
- **Comments System**: Collaborative image annotation
- **Activity Feeds**: Track catalog activity

This Product Catalog & Image Management module provides a comprehensive solution for managing product imagery with advanced search, filtering, and optimization capabilities, serving as the foundation for product discovery and selection throughout the application.
