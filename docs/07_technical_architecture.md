# Technical Architecture & Infrastructure

## Overview

The JocelEpress application is built on a modern Android architecture using Jetpack Compose, Firebase backend services, and follows clean architecture principles with MVVM pattern and repository abstraction.

## Architecture Patterns

### MVVM (Model-View-ViewModel) Pattern
- **Model**: Data classes and repository interfaces
- **View**: Jetpack Compose UI components
- **ViewModel**: Business logic and state management
- **Benefits**: Separation of concerns, testability, maintainability

### Repository Pattern
- **Data Abstraction**: Abstract data sources from business logic
- **Single Source of Truth**: Centralized data management
- **Caching Strategy**: Multi-level caching implementation
- **Error Handling**: Centralized error handling and retry logic

### Clean Architecture Principles
- **Dependency Inversion**: High-level modules don't depend on low-level modules
- **Single Responsibility**: Each class has a single, well-defined purpose
- **Open/Closed Principle**: Open for extension, closed for modification
- **Interface Segregation**: Clients depend only on interfaces they use

## Technology Stack

### Frontend Technologies
```kotlin
// Core Android
- Kotlin 2.1.0
- Android SDK (minSdk 23, targetSdk 34, compileSdk 35)
- Jetpack Compose (Material Design 3)
- Compose Navigation
- Compose Animation

// Architecture Components
- ViewModel & LiveData
- Lifecycle Components
- Paging 3 Library
- Room Database (for local caching)

// Dependency Injection
- Dagger Hilt 2.51.1
- Hilt Navigation Compose

// Image Loading & Processing
- Coil 2.6.0 (Compose integration)
- Custom image utilities
- Thumbnail generation
- Progressive loading
```

### Backend Technologies
```kotlin
// Firebase Services
- Firebase Auth (Authentication)
- Firestore (NoSQL Database)
- Firebase Storage (File Storage)
- Firebase Analytics
- Firebase Crashlytics

// Networking
- OkHttp 4.10.0
- Retrofit 2.9.0
- Gson Converter

// Data Serialization
- Moshi 1.15.1 (JSON parsing)
- Kotlin Serialization
```

### Development Tools
```kotlin
// Build System
- Gradle with Kotlin DSL
- Android Gradle Plugin
- Kotlin Compiler Plugin

// Code Quality
- Kotlin Lint
- ProGuard (for release builds)
- Crashlytics for error tracking

// Testing
- JUnit 4
- Espresso
- Compose Testing
```

## Project Structure

### Package Organization
```
com.tfkcolin.josyandchris/
├── data/                    # Data models and entities
├── repository/              # Data access layer
├── logic/                   # Business logic and utilities
├── ui/
│   ├── components/          # Reusable UI components
│   ├── screens/             # Screen-specific implementations
│   ├── theme/               # App theming and styling
│   └── data/                # UI-specific data classes
└── util/                    # Utility classes and helpers
```

### Module Dependencies
```
Application Layer (UI)
    ↓
Business Logic Layer (ViewModels)
    ↓
Repository Layer (Data Access)
    ↓
Data Layer (Firebase, Local Storage)
```

## Data Architecture

### Firebase Firestore Collections
```
/countries              # Country configuration data
/commands              # Customer orders and workflow
/cargos                # Shipping containers
/shipments             # Individual shipments
/transactions          # Financial transactions
/images_data           # Product catalog images
/users                 # User accounts and profiles
```

### Data Models Hierarchy
```
Command
├── ClientData          # Customer information
└── List<MiniProduct>   # Order products

Cargo
└── List<Shipment>      # Container shipments
    └── List<ShipmentProduct>  # Shipment contents

FinancialTransaction
├── commandId           # Link to orders
└── country             # Geographic assignment

ImageData
├── category            # Product categorization
├── genre               # Sub-categorization
└── thumbnailUrl        # Performance optimization
```

## Dependency Injection Architecture

### Hilt Module Configuration
```kotlin
@Module
@InstallIn(SingletonComponent::class)
object JACModule {
    // Firebase Services
    @Named("AUTH") fun provideFBAuth()
    @Named("STORAGE") fun provideFBStorage()
    
    // Firestore Collections
    @Named("COMMAND_DB") fun provideCommandCollection()
    @Named("CARGO_DB") fun provideCargoCollection()
    @Named("TRANSACTION_DB") fun provideTransactionCollection()
    @Named("IMAGES_DB") fun provideImageListCollection()
    @Named("COUNTRY_DB") fun provideCountryCollection()
    @Named("SHIPMENT_DB") fun provideShipmentCollection()
    
    // Storage References
    @Named("PRODUCT_IMAGES_STORAGE") fun provideFBImagesStorage()
    @Named("PRODUCT_IMAGES_STORAGE_V1") fun provideFBImagesStorageV1()
}
```

### Repository Injection
- Centralized repository configuration
- Named qualifiers for different data sources
- Singleton scope for shared resources
- Testable dependency injection

## Performance Architecture

### Caching Strategy
```kotlin
// Multi-Level Caching
1. Memory Cache (Coil + Custom)
   - Image caching in memory
   - Frequently accessed data
   - LRU eviction policy

2. Disk Cache (Local Storage)
   - Persistent image cache
   - Offline data storage
   - Configurable cache size

3. Database Cache (Room/Firestore)
   - Structured data caching
   - Query result caching
   - Automatic synchronization
```

### Image Loading Optimization
```kotlin
// Coil Configuration
ImageLoader.Builder(context)
    .memoryCache {
        MemoryCache.Builder(context)
            .maxSizePercent(0.25)  // 25% of app memory
            .build()
    }
    .diskCache {
        DiskCache.Builder()
            .maxSizeBytes(250_000_000L)  // 250MB
            .build()
    }
    .crossfade(true)
    .allowHardware(true)
```

### Pagination Implementation
```kotlin
// Paging 3 Integration
class FirestorePagingSource<T>(
    private val query: Query,
    private val pageSize: Int,
    private val transform: (DocumentSnapshot) -> T?
) : PagingSource<DocumentSnapshot, T>()

// Repository Usage
fun getPagedImageData(): Flow<PagingData<ImageData>> {
    return Pager(
        config = PagingConfig(pageSize = 20),
        pagingSourceFactory = { FirestorePagingSource(...) }
    ).flow
}
```

## Security Architecture

### Firebase Security Rules
```javascript
// Firestore Security Rules
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Role-based access control
    match /commands/{commandId} {
      allow read, write: if request.auth != null 
        && request.auth.token.role in ['admin', 'employee'];
    }
    
    match /transactions/{transactionId} {
      allow read, write: if request.auth != null 
        && request.auth.token.role == 'admin';
    }
  }
}
```

### Authentication Flow
```kotlin
// JWT Token Validation
LaunchedEffect(user) {
    user?.let {
        val token = Tasks.await(it.getIdToken(false))
        val role = token.claims["role"] as String?
        // Configure role-based permissions
    }
}
```

## Error Handling Architecture

### Centralized Error Handling
```kotlin
object FirebaseRepository {
    class FetchException(message: String?, cause: Throwable?) : Exception(message, cause)
    
    suspend fun <T> tryFetch(
        onError: ((String?) -> Unit)? = null,
        function: suspend () -> T
    ): T = try {
        withContext(Dispatchers.IO) { function() }
    } catch (e: Exception) {
        throw FetchException(handleFirebaseExceptions(onError, e), e.cause)
    }
}
```

### Error Types Handling
- **Network Errors**: Connectivity and timeout handling
- **Authentication Errors**: Token expiration and refresh
- **Firestore Errors**: Database operation failures
- **Storage Errors**: File upload/download failures
- **Validation Errors**: Data validation failures

## State Management Architecture

### Compose State Management
```kotlin
// ViewModel State
class CommandDetailsViewModel : ViewModel() {
    private val _uiState = MutableStateFlow(CommandDetailsUiState())
    val uiState: StateFlow<CommandDetailsUiState> = _uiState.asStateFlow()
    
    var changeMade by mutableStateOf(false)
        private set
}

// Compose Integration
@Composable
fun CommandDetailsScreen(viewModel: CommandDetailsViewModel = hiltViewModel()) {
    val uiState by viewModel.uiState.collectAsState()
    // UI implementation
}
```

### LiveData Integration
```kotlin
// Real-time Data Updates
class CommandListLiveData(private val query: Query) : 
    LiveData<List<Command>>(), EventListener<QuerySnapshot> {
    
    override fun onEvent(value: QuerySnapshot?, error: FirebaseFirestoreException?) {
        // Handle real-time updates
    }
}
```

## Build Configuration

### Gradle Configuration
```kotlin
android {
    compileSdk 35
    
    defaultConfig {
        applicationId "com.tfkcolin.josyandchris"
        minSdk 23
        targetSdk 34
        versionCode 50
        versionName "1.3.0"
    }
    
    buildFeatures {
        compose true
    }
    
    composeOptions {
        kotlinCompilerExtensionVersion '1.4.4'
    }
}
```

### Dependency Management
```kotlin
dependencies {
    // Firebase BOM for version alignment
    implementation platform('com.google.firebase:firebase-bom:33.12.0')
    
    // Compose BOM for UI components
    implementation platform('androidx.compose:compose-bom:2023.10.01')
    
    // Hilt for dependency injection
    implementation "com.google.dagger:hilt-android:2.51.1"
    kapt "com.google.dagger:hilt-android-compiler:2.51.1"
}
```

## Testing Architecture

### Testing Strategy
```kotlin
// Unit Tests
@Test
fun `command creation should validate required fields`() {
    // Test business logic
}

// Integration Tests
@Test
fun `repository should handle network errors gracefully`() {
    // Test repository layer
}

// UI Tests
@Test
fun `login screen should display error for invalid credentials`() {
    // Test UI components
}
```

### Testing Tools
- **JUnit 4**: Unit testing framework
- **Espresso**: UI testing framework
- **Compose Testing**: Compose-specific testing
- **Mockito**: Mocking framework
- **Truth**: Assertion library

## Deployment Architecture

### Build Variants
```kotlin
buildTypes {
    debug {
        applicationIdSuffix ".debug"
        debuggable true
        // Debug-specific configuration
    }
    
    release {
        minifyEnabled true
        proguardFiles getDefaultProguardFile('proguard-android-optimize.txt')
        // Release optimization
    }
}
```

### CI/CD Pipeline (Recommended for Rebuild)
1. **Source Control**: Git with feature branches
2. **Build Automation**: GitHub Actions or GitLab CI
3. **Testing**: Automated unit and integration tests
4. **Code Quality**: Static analysis and linting
5. **Deployment**: Automated deployment to Firebase App Distribution
6. **Monitoring**: Crashlytics and Analytics integration

## Performance Monitoring

### Current Monitoring
- **Firebase Crashlytics**: Crash reporting and analysis
- **Firebase Analytics**: User behavior tracking
- **Performance Monitoring**: App performance metrics
- **Custom Logging**: Application-specific logging

### Optimization Opportunities (Rebuild)
- **Real-time Performance Monitoring**: Live performance dashboards
- **Advanced Analytics**: Business intelligence integration
- **A/B Testing**: Feature experimentation framework
- **User Experience Monitoring**: UX performance tracking
- **Cost Monitoring**: Firebase usage and cost tracking

This technical architecture provides a solid foundation for the current application while identifying opportunities for enhancement in the rebuild to address scalability, maintainability, and observability requirements.
