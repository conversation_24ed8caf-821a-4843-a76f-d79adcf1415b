# UI/UX Patterns & Design System

## Overview

The JocelEpress application implements a comprehensive design system based on Material Design 3 principles with custom JAC-prefixed components. The UI follows modern Android design patterns with Jetpack Compose and provides a consistent, accessible user experience across all modules.

## Design System Foundation

### Material Design 3 Implementation
- **Color System**: Dynamic color theming with light/dark mode support
- **Typography**: Consistent text styling with Google Fonts integration
- **Component Library**: Material 3 components with custom extensions
- **Motion Design**: Smooth animations and transitions
- **Accessibility**: Full accessibility support with semantic descriptions

### Custom Component Prefix
All custom components use the "JAC" prefix (JocelEpress Application Components):
- Consistent naming convention
- Easy identification of custom vs. standard components
- Maintainable component library
- Clear component ownership

## Core UI Components

### Navigation Components

#### JACTopAppBar
```kotlin
@Composable
fun JACTopAppBar(
    title: String,
    navigationIcon: @Composable (() -> Unit)? = null,
    actions: @Composable RowScope.() -> Unit = {},
    colors: TopAppBarColors = TopAppBarDefaults.topAppBarColors()
)
```
- **Purpose**: Consistent top navigation across screens
- **Features**: Title display, navigation controls, action buttons
- **Customization**: Color theming, icon placement, action integration

#### JACBottomAppBar
```kotlin
@Composable
fun JACBottomAppBar(
    actions: @Composable RowScope.() -> Unit = {},
    floatingActionButton: @Composable (() -> Unit)? = null
)
```
- **Purpose**: Bottom navigation and primary actions
- **Features**: Action buttons, FAB integration, responsive layout
- **Usage**: Main navigation, quick actions, context-specific tools

#### JACFloatingActionButton
```kotlin
@Composable
fun JACFloatingActionButton(
    onClick: () -> Unit,
    icon: @Composable () -> Unit,
    contentDescription: String? = null
)
```
- **Purpose**: Primary action button with consistent styling
- **Features**: Material 3 styling, accessibility support, animation
- **Integration**: Bottom app bar integration, screen-specific actions

### Data Display Components

#### JACCommandItem
```kotlin
@Composable
fun JACCommandItem(
    command: Command,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
)
```
- **Purpose**: Display command information in lists
- **Features**: Customer info, status indicator, product count, pricing
- **Layout**: Card-based design with clear information hierarchy

#### JACCargoItem
```kotlin
@Composable
fun JACCargoItem(
    cargo: Cargo,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
)
```
- **Purpose**: Display cargo information with status
- **Features**: Route display, status indicator, date information
- **Design**: Consistent with command item styling

#### JACTransactionItem
```kotlin
@Composable
fun JACTransactionItem(
    transaction: FinancialTransaction,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
)
```
- **Purpose**: Financial transaction display
- **Features**: Amount display, transaction type, date, country
- **Styling**: Color-coded by transaction type (income/expense)

### Status and Progress Components

#### JACCurrentStepView
```kotlin
@Composable
fun JACCurrentStepView(
    currentStep: CommandStep,
    modifier: Modifier = Modifier
)
```
- **Purpose**: Visual workflow progress indicator
- **Features**: Step progression, current status highlight, icons
- **Design**: Horizontal stepper with clear visual feedback

#### JACCargoStatusView
```kotlin
@Composable
fun JACCargoStatusView(
    status: CargoStatus,
    modifier: Modifier = Modifier
)
```
- **Purpose**: Cargo status visualization
- **Features**: Status-specific colors, icons, labels
- **Integration**: Consistent with command status styling

#### JACShipmentStatusView
```kotlin
@Composable
fun JACShipmentStatusView(
    status: ShipmentStatus,
    modifier: Modifier = Modifier
)
```
- **Purpose**: Shipment status display
- **Features**: Multi-stage status indication, progress visualization
- **Design**: Compact status indicator for list items

### Input and Form Components

#### JACTextFieldWithButton
```kotlin
@Composable
fun JACTextFieldWithButton(
    value: String,
    onValueChange: (String) -> Unit,
    label: String,
    buttonText: String,
    onButtonClick: () -> Unit,
    modifier: Modifier = Modifier
)
```
- **Purpose**: Text input with associated action button
- **Features**: Integrated button, validation support, consistent styling
- **Usage**: Search fields, quick entry forms, action-based inputs

#### JACImagePicker
```kotlin
@Composable
fun JACImagePicker(
    onImageSelected: (Uri) -> Unit,
    modifier: Modifier = Modifier
)
```
- **Purpose**: Image selection interface
- **Features**: Gallery integration, camera support, preview
- **Design**: Intuitive image selection with visual feedback

### Data Management Components

#### JACEditClientData
```kotlin
@Composable
fun JACEditClientData(
    clientData: ClientData,
    onClientDataChange: (ClientData) -> Unit,
    modifier: Modifier = Modifier
)
```
- **Purpose**: Customer information editing interface
- **Features**: Form validation, real-time updates, error handling
- **Layout**: Organized form layout with clear field grouping

#### JACEditProducts
```kotlin
@Composable
fun JACEditProducts(
    products: List<MiniProduct>,
    onProductsChange: (List<MiniProduct>) -> Unit,
    modifier: Modifier = Modifier
)
```
- **Purpose**: Product list management interface
- **Features**: Add/remove products, quantity editing, price management
- **Design**: Dynamic list with inline editing capabilities

### Image and Media Components

#### JACImage
```kotlin
@Composable
fun JACImage(
    imageUrl: String?,
    contentDescription: String?,
    modifier: Modifier = Modifier,
    contentScale: ContentScale = ContentScale.Crop
)
```
- **Purpose**: Optimized image display with caching
- **Features**: Progressive loading, error handling, placeholder support
- **Performance**: Coil integration with custom caching strategy

#### JACImageDataCreator
```kotlin
@Composable
fun JACImageDataCreator(
    imageData: ImageData,
    onImageDataChange: (ImageData) -> Unit,
    enablePickImage: Boolean = true,
    modifier: Modifier = Modifier
)
```
- **Purpose**: Image metadata creation and editing
- **Features**: Category/genre selection, image upload, metadata editing
- **Integration**: Product catalog management, image organization

### Overview and Summary Components

#### JACCommandOverview
```kotlin
@Composable
fun JACCommandOverview(
    commands: List<Command>,
    onNavigateToCommandScreen: (Int) -> Unit,
    modifier: Modifier = Modifier
)
```
- **Purpose**: Command statistics and quick navigation
- **Features**: Status-based counts, quick filters, visual summaries
- **Design**: Card-based layout with clear metrics display

#### JACCargoOverview
```kotlin
@Composable
fun JACCargoOverview(
    cargos: List<Cargo>,
    onNavigateToCargoScreen: (Int) -> Unit,
    modifier: Modifier = Modifier
)
```
- **Purpose**: Cargo statistics and navigation
- **Features**: Status summaries, quick access, performance metrics
- **Layout**: Consistent with command overview styling

### Utility Components

#### JACSnackbarHost
```kotlin
@Composable
fun JACSnackbarHost(
    hostState: SnackbarHostState,
    variant: SnackbarType,
    modifier: Modifier = Modifier
)
```
- **Purpose**: Consistent snackbar messaging
- **Features**: Type-based styling (success, error, info), auto-dismiss
- **Design**: Material 3 snackbar with custom color coding

#### JACTotalPriceDisplay
```kotlin
@Composable
fun JACTotalPriceDisplay(
    totalPrice: Int,
    currency: String,
    modifier: Modifier = Modifier
)
```
- **Purpose**: Consistent price display formatting
- **Features**: Currency formatting, large number handling, styling
- **Usage**: Financial summaries, order totals, transaction amounts

## Screen Layout Patterns

### List Screen Pattern
```kotlin
@Composable
fun ListScreen<T>(
    items: List<T>,
    onItemClick: (T) -> Unit,
    itemContent: @Composable (T) -> Unit,
    floatingActionButton: @Composable (() -> Unit)? = null
) {
    Scaffold(
        topBar = { JACTopAppBar(...) },
        bottomBar = { JACBottomAppBar(...) },
        floatingActionButton = floatingActionButton
    ) { paddingValues ->
        LazyColumn(
            contentPadding = paddingValues,
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            items(items) { item ->
                itemContent(item)
            }
        }
    }
}
```

### Detail Screen Pattern
```kotlin
@Composable
fun DetailScreen(
    title: String,
    onNavigateBack: () -> Unit,
    actions: @Composable RowScope.() -> Unit = {},
    content: @Composable (PaddingValues) -> Unit
) {
    Scaffold(
        topBar = {
            JACTopAppBar(
                title = title,
                navigationIcon = {
                    IconButton(onClick = onNavigateBack) {
                        Icon(Icons.Default.ArrowBack, "Back")
                    }
                },
                actions = actions
            )
        }
    ) { paddingValues ->
        content(paddingValues)
    }
}
```

### Form Screen Pattern
```kotlin
@Composable
fun FormScreen(
    title: String,
    onSave: () -> Unit,
    onCancel: () -> Unit,
    content: @Composable (PaddingValues) -> Unit
) {
    Scaffold(
        topBar = {
            JACTopAppBar(
                title = title,
                actions = {
                    TextButton(onClick = onSave) {
                        Text("Save")
                    }
                }
            )
        }
    ) { paddingValues ->
        content(paddingValues)
    }
}
```

## Theme and Styling

### Color System
```kotlin
// Light Theme Colors
val LightColorScheme = lightColorScheme(
    primary = Color(0xFF1976D2),
    onPrimary = Color.White,
    secondary = Color(0xFF03DAC6),
    onSecondary = Color.Black,
    // ... additional colors
)

// Dark Theme Colors
val DarkColorScheme = darkColorScheme(
    primary = Color(0xFF90CAF9),
    onPrimary = Color.Black,
    secondary = Color(0xFF03DAC6),
    onSecondary = Color.Black,
    // ... additional colors
)
```

### Typography System
```kotlin
val Typography = Typography(
    displayLarge = TextStyle(
        fontFamily = FontFamily.Default,
        fontWeight = FontWeight.Normal,
        fontSize = 57.sp,
        lineHeight = 64.sp,
        letterSpacing = (-0.25).sp,
    ),
    // ... additional text styles
)
```

### Component Styling
```kotlin
// Custom component defaults
object JACDefaults {
    val CardElevation = 4.dp
    val CornerRadius = 8.dp
    val Spacing = 16.dp
    val SmallSpacing = 8.dp
    val LargeSpacing = 24.dp
}
```

## Animation and Motion

### Transition Animations
```kotlin
// Screen transitions
val slideInTransition = slideInHorizontally(
    initialOffsetX = { it },
    animationSpec = tween(300)
)

val slideOutTransition = slideOutHorizontally(
    targetOffsetX = { -it },
    animationSpec = tween(300)
)
```

### Component Animations
```kotlin
// Loading animations
@Composable
fun LoadingAnimation() {
    val infiniteTransition = rememberInfiniteTransition()
    val rotation by infiniteTransition.animateFloat(
        initialValue = 0f,
        targetValue = 360f,
        animationSpec = infiniteRepeatable(
            animation = tween(1000, easing = LinearEasing)
        )
    )
    // Animation implementation
}
```

## Accessibility Features

### Semantic Descriptions
```kotlin
@Composable
fun AccessibleComponent() {
    Button(
        onClick = { /* action */ },
        modifier = Modifier.semantics {
            contentDescription = "Add new command"
            role = Role.Button
        }
    ) {
        Text("Add")
    }
}
```

### Focus Management
```kotlin
@Composable
fun FocusableForm() {
    val focusRequester = remember { FocusRequester() }
    
    TextField(
        value = value,
        onValueChange = onValueChange,
        modifier = Modifier
            .focusRequester(focusRequester)
            .onKeyEvent { /* handle key events */ }
    )
}
```

## Responsive Design

### Adaptive Layouts
```kotlin
@Composable
fun AdaptiveLayout(
    windowSizeClass: WindowSizeClass
) {
    when (windowSizeClass.widthSizeClass) {
        WindowWidthSizeClass.Compact -> {
            // Phone layout
            CompactLayout()
        }
        WindowWidthSizeClass.Medium -> {
            // Tablet portrait layout
            MediumLayout()
        }
        WindowWidthSizeClass.Expanded -> {
            // Tablet landscape/desktop layout
            ExpandedLayout()
        }
    }
}
```

### Orientation Handling
```kotlin
@Composable
fun OrientationAwareComponent() {
    val configuration = LocalConfiguration.current
    
    when (configuration.orientation) {
        Configuration.ORIENTATION_LANDSCAPE -> {
            LandscapeLayout()
        }
        else -> {
            PortraitLayout()
        }
    }
}
```

## Performance Optimizations

### Lazy Loading
```kotlin
@Composable
fun OptimizedList(items: List<Item>) {
    LazyColumn {
        items(
            items = items,
            key = { it.id }  // Stable keys for recomposition optimization
        ) { item ->
            ItemComponent(item)
        }
    }
}
```

### State Management
```kotlin
@Composable
fun OptimizedComponent() {
    // Minimize recomposition scope
    val expensiveValue by remember {
        derivedStateOf {
            // Expensive calculation
            calculateExpensiveValue()
        }
    }
    
    // Use stable references
    val stableCallback = remember {
        { /* callback implementation */ }
    }
}
```

This comprehensive UI/UX pattern documentation provides the foundation for maintaining design consistency and user experience quality throughout the JocelEpress application rebuild.
