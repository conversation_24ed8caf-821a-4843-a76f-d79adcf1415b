# Cargo & Logistics Management Module

## Overview

The Cargo & Logistics Management module handles the physical transportation and tracking of goods across international routes. It manages cargo containers, individual shipments, and provides comprehensive logistics coordination for the entire supply chain.

## Business Purpose

- **Primary Function**: Manage cargo containers and shipments from origin to destination
- **Business Value**: Complete visibility over logistics operations and shipment tracking
- **User Benefits**: Real-time cargo tracking, efficient shipment management, route optimization

## Data Models

### Cargo Entity
```kotlin
data class Cargo(
    val id: String = "",
    val origin: String = "",
    val destination: String = "",
    val statusIndex: Int = 0,
    val departureDate: Date? = null,
    val estimatedArrivalDate: Date? = null,
    val actualArrivalDate: Date? = null,
    val searchIndex: String = "", // For better search functionality
    val created: Date = Calendar.getInstance().time
)
```

### Shipment Entity
```kotlin
data class Shipment(
    val id: String = "",
    val cargoId: String = "",
    val clientName: String = "",
    val clientPhone: String = "",
    val weightKg: Double = 0.0,
    val volumeCbm: Double = 0.0,
    val amountDue: Double = 0.0,
    val statusIndex: Int = 0,
    val products: List<ShipmentProduct> = listOf(),
    val created: Date = Calendar.getInstance().time
)
```

### ShipmentProduct Entity
```kotlin
data class ShipmentProduct(
    val name: String = "",
    val quantity: Int = 0,
    val description: String = ""
)
```

## Cargo Status Management

### CargoStatus Workflow (3-Stage Process)

#### 1. LOADING
- **Purpose**: Cargo container being loaded with shipments
- **Activities**:
  - Shipment collection and verification
  - Container loading and organization
  - Documentation preparation
  - Weight and volume calculation
  - Departure preparation

#### 2. IN_TRANSIT
- **Purpose**: Cargo en route to destination
- **Activities**:
  - Route tracking and monitoring
  - Progress updates and notifications
  - Estimated arrival time updates
  - Issue resolution and communication
  - Customer status updates

#### 3. ARRIVED
- **Purpose**: Cargo arrived at destination
- **Activities**:
  - Arrival confirmation and documentation
  - Unloading coordination
  - Shipment distribution preparation
  - Customer notification
  - Final delivery scheduling

## Shipment Status Management

### ShipmentStatus Workflow (4-Stage Process)

#### 1. PENDING
- **Purpose**: Shipment awaiting cargo assignment
- **Activities**:
  - Shipment registration and documentation
  - Customer information verification
  - Product listing and verification
  - Weight and volume measurement
  - Cargo assignment waiting

#### 2. LOADED
- **Purpose**: Shipment loaded into cargo container
- **Activities**:
  - Container assignment confirmation
  - Loading documentation
  - Tracking number assignment
  - Customer notification
  - Transit preparation

#### 3. IN_TRANSIT
- **Purpose**: Shipment en route with cargo
- **Activities**:
  - Real-time location tracking
  - Progress monitoring
  - Customer updates
  - Issue tracking and resolution
  - Delivery preparation

#### 4. DELIVERED
- **Purpose**: Shipment delivered to customer
- **Activities**:
  - Delivery confirmation
  - Customer signature collection
  - Payment processing
  - Feedback collection
  - Shipment closure

## User Interface Components

### CargoListScreen
- **Purpose**: Display and manage cargo containers
- **Features**:
  - Status-based filtering
  - Pagination for large datasets
  - Search by origin/destination
  - Real-time status updates
  - Quick action buttons

### CargoDetailsScreen
- **Purpose**: Comprehensive cargo management interface
- **Features**:
  - Complete cargo information display
  - Shipment list management
  - Status progression controls
  - Date management (departure, arrival)
  - Geolocation integration
  - Shipment addition and removal

### ShipmentDetailsScreen
- **Purpose**: Individual shipment management
- **Features**:
  - Detailed shipment information
  - Product list management
  - Customer contact information
  - Weight and volume tracking
  - Amount due calculation
  - Status updates

### Cargo Components
- **JACCargoItem**: List item with key cargo information
- **JACCargoOverview**: Summary statistics and quick actions
- **JACCargoStatusView**: Visual status indicator
- **JACShipmentItem**: Individual shipment display
- **JACShipmentStatusView**: Shipment status indicator

## Key Features

### 1. Cargo Management
- **Route Planning**: Origin and destination management
- **Container Tracking**: Real-time location and status
- **Capacity Management**: Weight and volume optimization
- **Schedule Management**: Departure and arrival dates
- **Search Optimization**: Enhanced search capabilities

### 2. Shipment Management
- **Customer Information**: Name, phone, contact details
- **Product Tracking**: Detailed product lists and descriptions
- **Weight/Volume Calculation**: Precise measurement tracking
- **Financial Tracking**: Amount due and payment status
- **Status Progression**: Automated workflow management

### 3. Geolocation Integration
- **Origin Selection**: Geographic location picker
- **Destination Selection**: Target location management
- **Route Optimization**: Efficient path planning
- **Real-time Tracking**: GPS-based location updates
- **Map Integration**: Visual route and location display

### 4. Logistics Coordination
- **Multi-shipment Containers**: Multiple shipments per cargo
- **Cross-reference Tracking**: Cargo-shipment relationships
- **Automated Notifications**: Status change alerts
- **Documentation Management**: Shipping papers and customs
- **Performance Analytics**: Delivery time analysis

### 5. Cargo & Shipment Link Sharing
- **Cargo Tracking Links**: Shareable URLs for cargo status monitoring
- **Shipment Tracking Links**: Individual shipment tracking for customers
- **Real-time Location Updates**: GPS-based tracking information
- **Estimated Delivery Times**: Dynamic delivery predictions
- **Multi-stakeholder Access**: Different views for customers, partners, and internal teams
- **Notification Integration**: Automatic tracking link distribution

## Business Rules

### Cargo Management Rules
- Cargo must have valid origin and destination
- Departure date cannot be in the past (for new cargos)
- Estimated arrival must be after departure date
- Status progression must follow defined workflow
- Search index automatically generated for optimization

### Shipment Management Rules
- Shipments must be assigned to valid cargo
- Weight and volume must be positive values
- Customer contact information required
- Amount due must be non-negative
- Product list cannot be empty

### Logistics Coordination Rules
- Shipments can only be added to LOADING status cargos
- Cargo status affects all contained shipments
- Delivered shipments cannot be modified
- Weight/volume limits enforced per cargo

## User Interactions

### Creating New Cargo
1. Navigate to Cargo screen
2. Click "Add Cargo" button
3. Select origin location (with geolocation)
4. Select destination location
5. Set departure and estimated arrival dates
6. Save cargo (LOADING status)

### Managing Cargo Shipments
1. Open cargo details
2. View current shipment list
3. Add new shipments to cargo
4. Modify shipment details
5. Update cargo status
6. Track progress through workflow

### Shipment Lifecycle Management
1. Create shipment with customer details
2. Add products and calculate weight/volume
3. Assign to appropriate cargo
4. Track through transit stages
5. Confirm delivery and payment
6. Close shipment record

## Integration Points

### With Command Management
- Link shipments to customer orders
- Coordinate product delivery status
- Synchronize customer information
- Track order fulfillment progress

### With Financial System
- Calculate shipping costs
- Track payment due amounts
- Generate shipping invoices
- Monitor logistics profitability

### With Geolocation Services
- Origin and destination selection
- Route planning and optimization
- Real-time tracking capabilities
- Map visualization integration

## Performance Considerations

### Current Implementation
- Pagination for large cargo/shipment lists
- Efficient Firestore queries with indexing
- Real-time updates with LiveData
- Background synchronization

### Optimization Opportunities (Rebuild)
- Geographic indexing for location queries
- Optimized route calculation algorithms
- Enhanced real-time tracking
- Predictive analytics for delivery times
- Automated status updates via IoT integration

## User Roles & Permissions

### Admin
- Full cargo and shipment management
- Route planning and optimization
- Performance analytics access
- System configuration

### Employee
- Cargo creation and management
- Shipment assignment and tracking
- Customer communication
- Status updates

### Deliverer
- Delivery confirmation
- Shipment status updates
- Customer interaction
- Mobile-optimized interface

## Cargo & Shipment Link Sharing System

### Overview
The Cargo & Shipment Link Sharing system provides comprehensive tracking capabilities for logistics operations, enabling customers, partners, and stakeholders to monitor cargo and shipment progress through secure, real-time tracking links.

### Cargo Tracking Links Implementation
```kotlin
data class CargoShareLink(
    val id: String = UUID.randomUUID().toString(),
    val cargoId: String,
    val token: String = generateSecureToken(),
    val accessLevel: AccessLevel = AccessLevel.CUSTOMER,
    val expiresAt: Timestamp,
    val allowedViewers: List<String> = emptyList(), // Phone numbers or emails
    val isActive: Boolean = true,
    val createdBy: String,
    val created: Timestamp = Timestamp.now()
)

enum class AccessLevel {
    CUSTOMER,    // Basic tracking info
    PARTNER,     // Detailed logistics info
    INTERNAL     // Full operational data
}

// Customer view of cargo information
data class CustomerCargoView(
    val id: String,
    val trackingNumber: String,
    val origin: String,
    val destination: String,
    val status: CargoStatus,
    val statusDescription: String,
    val currentLocation: LocationInfo?,
    val departureDate: Timestamp?,
    val estimatedArrival: Timestamp?,
    val actualArrival: Timestamp?,
    val shipments: List<CustomerShipmentSummary>,
    val timeline: List<CargoStatusUpdate>,
    val lastUpdated: Timestamp
)
```

### Shipment Tracking Links Implementation
```kotlin
data class ShipmentShareLink(
    val id: String = UUID.randomUUID().toString(),
    val shipmentId: String,
    val cargoId: String,
    val token: String = generateSecureToken(),
    val customerPhone: String, // Linked to specific customer
    val expiresAt: Timestamp,
    val isActive: Boolean = true,
    val createdBy: String,
    val created: Timestamp = Timestamp.now()
)

// Customer view of shipment information
data class CustomerShipmentView(
    val id: String,
    val trackingNumber: String,
    val customerName: String,
    val status: ShipmentStatus,
    val statusDescription: String,
    val products: List<ShipmentProduct>,
    val weightKg: Double,
    val volumeCbm: Double,
    val amountDue: BigDecimal,
    val currency: String,
    val cargo: CargoSummary,
    val timeline: List<ShipmentStatusUpdate>,
    val estimatedDelivery: Timestamp?,
    val lastUpdated: Timestamp
)

data class CargoSummary(
    val trackingNumber: String,
    val origin: String,
    val destination: String,
    val currentStatus: CargoStatus,
    val currentLocation: LocationInfo?
)
```

### Real-time Location Tracking
```kotlin
data class LocationInfo(
    val latitude: Double,
    val longitude: Double,
    val address: String,
    val city: String,
    val country: String,
    val timestamp: Timestamp,
    val accuracy: Double? = null
)

// Location tracking service
interface LocationTrackingService {
    suspend fun updateCargoLocation(cargoId: String, location: LocationInfo)
    suspend fun getCargoLocation(cargoId: String): LocationInfo?
    suspend fun getLocationHistory(cargoId: String, limit: Int = 50): List<LocationInfo>
    suspend fun estimateArrivalTime(cargoId: String): Timestamp?
}

// Real-time location updates
class CargoLocationUpdater {
    suspend fun broadcastLocationUpdate(cargoId: String, location: LocationInfo) {
        // Update cargo record
        cargoRepository.updateLocation(cargoId, location)

        // Notify all active tracking links
        val activeLinks = linkRepository.getActiveLinksForCargo(cargoId)
        activeLinks.forEach { link ->
            notificationService.sendLocationUpdate(link, location)
        }

        // Update estimated arrival times
        val newEstimate = locationService.estimateArrivalTime(cargoId)
        if (newEstimate != null) {
            cargoRepository.updateEstimatedArrival(cargoId, newEstimate)
        }
    }
}
```

### Multi-Level Access Control
```kotlin
class CargoAccessController {
    suspend fun getCargoView(token: String, requestIP: String): CargoViewResult {
        val link = validateCargoLink(token, requestIP)
        if (link !is ValidationResult.Valid) {
            return CargoViewResult.AccessDenied
        }

        val cargo = cargoRepository.getById(link.data.cargoId)

        return when (link.data.accessLevel) {
            AccessLevel.CUSTOMER -> CargoViewResult.Success(
                cargo.toCustomerView()
            )
            AccessLevel.PARTNER -> CargoViewResult.Success(
                cargo.toPartnerView()
            )
            AccessLevel.INTERNAL -> CargoViewResult.Success(
                cargo.toInternalView()
            )
        }
    }
}

// Different view levels
fun Cargo.toCustomerView(): CustomerCargoView {
    return CustomerCargoView(
        id = id,
        trackingNumber = generateTrackingNumber(),
        origin = origin,
        destination = destination,
        status = CargoStatus.values()[statusIndex],
        statusDescription = getStatusDescription(),
        currentLocation = getCurrentLocation(),
        departureDate = departureDate,
        estimatedArrival = estimatedArrivalDate,
        actualArrival = actualArrivalDate,
        shipments = shipments.map { it.toCustomerSummary() },
        timeline = getStatusTimeline(),
        lastUpdated = Timestamp.now()
    )
}
```

## Technical Implementation

### Repository Pattern
- `FirestoreRepository` for data persistence
- `CargoListLiveData` for real-time cargo updates
- `ShipmentListLiveData` for shipment tracking
- `CargoLinkRepository` for tracking link management
- `LocationTrackingRepository` for GPS data
- `NotificationRepository` for tracking notifications
- Geolocation services integration

### ViewModel Architecture
- `CargoDetailsViewModel` for cargo business logic
- `CargoListViewModel` for list management
- `CargoTrackingViewModel` for customer tracking portal
- `LocationTrackingViewModel` for real-time updates
- `ShareLinkViewModel` for link generation and management
- State management with Compose
- Async operations with coroutines

### UI Components
- Material Design 3 components
- Custom JAC-prefixed logistics components
- `JACCargoTrackingView` for customer portal
- `JACLocationMapView` for real-time tracking
- `JACShareCargoDialog` for link generation
- `JACShipmentTrackingCard` for shipment details
- Responsive layout for different screen sizes
- Accessibility support for all users

## Geolocation Features

### Location Selection
- Interactive map interface
- Search-based location picker
- GPS coordinate capture
- Address validation and formatting
- Favorite locations management

### Route Management
- Origin to destination routing
- Multiple waypoint support
- Distance and time calculation
- Route optimization algorithms
- Real-time traffic integration

### Tracking Capabilities
- Real-time cargo location updates
- Shipment progress monitoring
- Delivery confirmation with location
- Historical route tracking
- Performance analytics

## Analytics & Reporting

### Cargo Performance
- Average delivery times
- Route efficiency analysis
- Capacity utilization metrics
- Cost per shipment analysis
- Customer satisfaction tracking

### Shipment Analytics
- Delivery success rates
- Weight and volume trends
- Customer behavior patterns
- Revenue per shipment
- Geographic distribution analysis

This Cargo & Logistics Management module provides comprehensive supply chain visibility and control, enabling efficient management of international shipping operations with real-time tracking and automated workflow management.
