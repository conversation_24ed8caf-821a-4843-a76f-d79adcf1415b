package com.tfkcolin.joceladmin.di

import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.CollectionReference
import com.google.firebase.storage.FirebaseStorage
import com.google.firebase.storage.StorageReference
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Named
import javax.inject.Singleton

/**
 * Hilt module for Firebase services
 * Based on the architecture documentation's Firebase configuration
 */
@Module
@InstallIn(SingletonComponent::class)
object FirebaseModule {

    @Provides
    @Singleton
    @Named("AUTH")
    fun provideFirebaseAuth(): FirebaseAuth = FirebaseAuth.getInstance()

    @Provides
    @Singleton
    fun provideFirebaseFirestore(): FirebaseFirestore = FirebaseFirestore.getInstance()

    @Provides
    @Singleton
    @Named("STORAGE")
    fun provideFirebaseStorage(): FirebaseStorage = FirebaseStorage.getInstance()

    // Firestore Collections
    @Provides
    @Singleton
    @Named("COMMAND_DB")
    fun provideCommandCollection(firestore: FirebaseFirestore): CollectionReference =
        firestore.collection("commands")

    @Provides
    @Singleton
    @Named("CARGO_DB")
    fun provideCargoCollection(firestore: FirebaseFirestore): CollectionReference =
        firestore.collection("cargos")

    @Provides
    @Singleton
    @Named("SHIPMENT_DB")
    fun provideShipmentCollection(firestore: FirebaseFirestore): CollectionReference =
        firestore.collection("shipments")

    @Provides
    @Singleton
    @Named("TRANSACTION_DB")
    fun provideTransactionCollection(firestore: FirebaseFirestore): CollectionReference =
        firestore.collection("transactions")

    @Provides
    @Singleton
    @Named("IMAGES_DB")
    fun provideImageListCollection(firestore: FirebaseFirestore): CollectionReference =
        firestore.collection("images")

    @Provides
    @Singleton
    @Named("PRODUCTS_DB")
    fun provideProductsCollection(firestore: FirebaseFirestore): CollectionReference =
        firestore.collection("products")

    @Provides
    @Singleton
    @Named("CATEGORIES_DB")
    fun provideCategoriesCollection(firestore: FirebaseFirestore): CollectionReference =
        firestore.collection("categories")

    @Provides
    @Singleton
    @Named("GENRES_DB")
    fun provideGenresCollection(firestore: FirebaseFirestore): CollectionReference =
        firestore.collection("genres")

    @Provides
    @Singleton
    @Named("COUNTRY_DB")
    fun provideCountryCollection(firestore: FirebaseFirestore): CollectionReference =
        firestore.collection("countries")

    @Provides
    @Singleton
    @Named("USER_DB")
    fun provideUserCollection(firestore: FirebaseFirestore): CollectionReference =
        firestore.collection("users")

    // Storage References
    @Provides
    @Singleton
    @Named("IMAGES_STORAGE")
    fun provideImagesStorage(
        @Named("STORAGE") storage: FirebaseStorage
    ): StorageReference = storage.reference.child("images")

    @Provides
    @Singleton
    @Named("THUMBNAILS_STORAGE")
    fun provideThumbnailsStorage(
        @Named("STORAGE") storage: FirebaseStorage
    ): StorageReference = storage.reference.child("thumbnails")

    @Provides
    @Singleton
    @Named("COMPRESSED_STORAGE")
    fun provideCompressedStorage(
        @Named("STORAGE") storage: FirebaseStorage
    ): StorageReference = storage.reference.child("compressed")

    @Provides
    @Singleton
    @Named("PRODUCT_IMAGES_STORAGE")
    fun provideProductImagesStorage(
        @Named("STORAGE") storage: FirebaseStorage
    ): StorageReference = storage.reference.child("product_images")

    @Provides
    @Singleton
    @Named("DOCUMENTS_STORAGE")
    fun provideDocumentsStorage(
        @Named("STORAGE") storage: FirebaseStorage
    ): StorageReference = storage.reference.child("documents")
}
