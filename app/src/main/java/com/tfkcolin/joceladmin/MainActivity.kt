package com.tfkcolin.joceladmin

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.navigation.compose.rememberNavController
import com.tfkcolin.joceladmin.navigation.JocelAdminNavigation
import com.tfkcolin.joceladmin.ui.theme.JocelAdminTheme
import dagger.hilt.android.AndroidEntryPoint

/**
 * Main activity for JocelAdmin app
 * Entry point with Hilt dependency injection
 */
@AndroidEntryPoint
class MainActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        setContent {
            JocelAdminTheme {
                Surface(
                    modifier = Modifier.fillMaxSize(),
                    color = MaterialTheme.colorScheme.background
                ) {
                    JocelAdminApp()
                }
            }
        }
    }
}

@Composable
fun JocelAdminApp() {
    val navController = rememberNavController()

    JocelAdminNavigation(
        navController = navController
    )
}

@Preview(showBackground = true)
@Composable
fun JocelAdminAppPreview() {
    JocelAdminTheme {
        JocelAdminApp()
    }
}