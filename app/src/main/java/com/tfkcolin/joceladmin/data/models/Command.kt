package com.tfkcolin.joceladmin.data.models

import kotlinx.serialization.Serializable
import java.util.Calendar
import java.util.Date

/**
 * Command data model representing customer orders
 * Implements the 6-stage workflow: Record → Buying → Received → Delivered → Ready → OK
 */
@Serializable
data class Command(
    val id: String = "",
    val commandNumber: String = "",
    val client: ClientData = ClientData(),
    val products: List<MiniProduct> = emptyList(),
    val commandStepIndex: Int = 0, // Maps to CommandStatus ordinal
    val observation: List<String> = emptyList(),
    val paymentProofImageUrl: String? = null,
    val proofUploaded: Boolean = false,
    val created: Long = Calendar.getInstance().timeInMillis,
    val updatedAt: Long = System.currentTimeMillis(),
    val createdBy: String = "",
    val assignedTo: String? = null,
    val country: String = "",
    val currency: String = "USD",
    val estimatedDeliveryDate: Long? = null,
    val actualDeliveryDate: Long? = null,
    val totalSellingAmount: Double = 0.0,
    val totalBuyingAmount: Double = 0.0,
    val isArchived: Boolean = false
) {
    /**
     * Get current command status
     */
    fun getStatus(): CommandStatus {
        return CommandStatus.values().getOrNull(commandStepIndex) ?: CommandStatus.RECORD
    }

    /**
     * Update command status
     */
    fun updateStatus(status: CommandStatus): Command {
        return copy(
            commandStepIndex = status.ordinal,
            updatedAt = System.currentTimeMillis()
        )
    }

    /**
     * Calculate total selling amount from products
     */
    fun calculateTotalSellingAmount(): Double {
        return products.sumOf { it.getTotalSellingPrice() }
    }

    /**
     * Calculate total buying amount from products
     */
    fun calculateTotalBuyingAmount(): Double {
        return products.sumOf { it.getTotalBuyingPrice() }
    }

    /**
     * Calculate profit margin
     */
    fun getProfitMargin(): Double {
        return calculateTotalSellingAmount() - calculateTotalBuyingAmount()
    }

    /**
     * Calculate profit percentage
     */
    fun getProfitPercentage(): Double {
        val buyingAmount = calculateTotalBuyingAmount()
        return if (buyingAmount > 0) {
            (getProfitMargin() / buyingAmount) * 100
        } else 0.0
    }

    /**
     * Check if command is completed
     */
    fun isCompleted(): Boolean {
        return getStatus() == CommandStatus.OK
    }

    /**
     * Check if payment proof is required
     */
    fun requiresPaymentProof(): Boolean {
        return getStatus().ordinal >= CommandStatus.READY.ordinal && !proofUploaded
    }

    /**
     * Add observation note
     */
    fun addObservation(note: String): Command {
        return copy(
            observation = observation + note,
            updatedAt = System.currentTimeMillis()
        )
    }

    /**
     * Update payment proof
     */
    fun updatePaymentProof(imageUrl: String): Command {
        return copy(
            paymentProofImageUrl = imageUrl,
            proofUploaded = true,
            updatedAt = System.currentTimeMillis()
        )
    }

    /**
     * Get formatted command number
     */
    fun getFormattedCommandNumber(): String {
        return if (commandNumber.isNotBlank()) commandNumber else "CMD-${id.take(8)}"
    }

    /**
     * Get command summary for display
     */
    fun getSummary(): String {
        return "${getFormattedCommandNumber()} - ${client.getDisplayName()}"
    }

    /**
     * Validate command data
     */
    fun isValid(): Boolean {
        return client.isValid() && products.isNotEmpty() && products.all { it.isValid() }
    }

    /**
     * Get formatted total amount
     */
    fun getFormattedTotalAmount(): String {
        return "$currency ${String.format("%.2f", calculateTotalSellingAmount())}"
    }

    /**
     * Check if command can progress to next status
     */
    fun canProgress(): Boolean {
        return getStatus().canProgress() && isValid()
    }

    /**
     * Check if command can regress to previous status
     */
    fun canRegress(): Boolean {
        return getStatus().canRegress()
    }
}


