package com.tfkcolin.joceladmin.data.models

import kotlinx.serialization.Serializable

/**
 * Financial transaction data model for multi-country financial management
 */
@Serializable
data class FinancialTransaction(
    val id: String = "0",
    val label: String = "",
    val price: Int = 0,
    val commandId: String? = null,
    val commandStepIndex: Int? = null,
    val country: String = "", // country where the transaction occurred
    val transactionTypeIndex: Int = 0, // Maps to TransactionType ordinal
    val marked: Boolean = false,
    val created: Long = java.util.Calendar.getInstance().timeInMillis,
    val updatedAt: Long = System.currentTimeMillis(),
    val createdBy: String = "",
    val currency: String = "USD",
    val exchangeRate: Double = 1.0,
    val category: String = "",
    val paymentMethod: String = "",
    val reference: String = "",
    val notes: String = ""
) {
    /**
     * Get transaction type
     */
    fun getTransactionType(): TransactionType {
        return TransactionType.fromIndex(transactionTypeIndex)
    }

    /**
     * Update transaction type
     */
    fun updateTransactionType(type: TransactionType): FinancialTransaction {
        return copy(
            transactionTypeIndex = type.ordinal,
            updatedAt = System.currentTimeMillis()
        )
    }

    /**
     * Get formatted price
     */
    fun getFormattedPrice(): String {
        return "$currency ${String.format("%.2f", price.toDouble())}"
    }

    /**
     * Check if transaction is income
     */
    fun isIncome(): Boolean {
        return getTransactionType() == TransactionType.INPUT
    }

    /**
     * Check if transaction is expense
     */
    fun isExpense(): Boolean {
        return getTransactionType() == TransactionType.OUTPUT
    }

    /**
     * Validate transaction data
     */
    fun isValid(): Boolean {
        return label.isNotBlank() &&
               price > 0 &&
               country.isNotBlank()
    }

    /**
     * Get transaction summary
     */
    fun getSummary(): String {
        val typeLabel = if (isIncome()) "Income" else "Expense"
        return "$label - $typeLabel ${getFormattedPrice()}"
    }

    /**
     * Check if transaction is linked to command
     */
    fun isLinkedToCommand(): Boolean {
        return commandId != null && commandId.isNotBlank()
    }

    /**
     * Get display label
     */
    fun getDisplayLabel(): String {
        return if (label.isNotBlank()) label else "Untitled Transaction"
    }
}



/**
 * Transaction status
 */
@Serializable
enum class TransactionStatus(val displayName: String) {
    PENDING("Pending"),
    APPROVED("Approved"),
    REJECTED("Rejected"),
    COMPLETED("Completed"),
    CANCELLED("Cancelled")
}

/**
 * Financial summary for a specific period and country
 */
@Serializable
data class FinancialSummary(
    val country: String = "",
    val currency: String = "USD",
    val period: String = "", // e.g., "2024-12", "2024-Q4"
    val totalIncome: Double = 0.0,
    val totalExpenses: Double = 0.0,
    val netProfit: Double = 0.0,
    val transactionCount: Int = 0,
    val incomeByCategory: Map<String, Double> = emptyMap(),
    val expensesByCategory: Map<String, Double> = emptyMap(),
    val monthlyTrend: List<MonthlyData> = emptyList()
)

/**
 * Monthly financial data for trends
 */
@Serializable
data class MonthlyData(
    val month: String = "", // e.g., "2024-12"
    val income: Double = 0.0,
    val expenses: Double = 0.0,
    val profit: Double = 0.0,
    val transactionCount: Int = 0
)



/**
 * Banking information for a country
 */
@Serializable
data class BankingInfo(
    val bankName: String = "",
    val accountNumber: String = "",
    val routingNumber: String = "",
    val swiftCode: String = "",
    val iban: String = "",
    val address: String = ""
)

/**
 * Financial dashboard statistics
 */
@Serializable
data class FinancialStats(
    val totalRevenue: Double = 0.0,
    val totalExpenses: Double = 0.0,
    val netProfit: Double = 0.0,
    val profitMargin: Double = 0.0,
    val revenueGrowth: Double = 0.0,
    val expenseGrowth: Double = 0.0,
    val transactionCount: Int = 0,
    val averageTransactionValue: Double = 0.0,
    val topCategories: List<FinancialCategorySummary> = emptyList(),
    val countryBreakdown: List<CountrySummary> = emptyList()
)

/**
 * Financial category summary for financial analysis
 */
@Serializable
data class FinancialCategorySummary(
    val category: String = "",
    val amount: Double = 0.0,
    val percentage: Double = 0.0,
    val transactionCount: Int = 0
)

/**
 * Country summary for financial analysis
 */
@Serializable
data class CountrySummary(
    val country: String = "",
    val revenue: Double = 0.0,
    val expenses: Double = 0.0,
    val profit: Double = 0.0,
    val transactionCount: Int = 0
)
