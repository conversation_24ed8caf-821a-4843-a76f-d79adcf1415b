package com.tfkcolin.joceladmin.data.models

import kotlinx.serialization.Serializable

/**
 * Product entity for catalog management
 */
@Serializable
data class Product(
    val id: String = "",
    val name: String = "",
    val description: String = "",
    val sku: String = "",
    val barcode: String = "",
    val category: String = "",
    val genre: String = "",
    val brand: String = "",
    val supplier: String = "",
    val price: Double = 0.0,
    val cost: Double = 0.0,
    val currency: String = "USD",
    val weight: Double = 0.0,
    val dimensions: ProductDimensions = ProductDimensions(),
    val stock: Int = 0,
    val minStock: Int = 0,
    val maxStock: Int = 0,
    val isActive: Boolean = true,
    val isAvailable: Boolean = true,
    val isFeatured: Boolean = false,
    val tags: List<String> = emptyList(),
    val images: List<String> = emptyList(), // Image IDs
    val primaryImageId: String = "",
    val variants: List<ProductVariant> = emptyList(),
    val specifications: Map<String, String> = emptyMap(),
    val seoData: ProductSEO = ProductSEO(),
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis(),
    val createdBy: String = "",
    val updatedBy: String = "",
    val version: Int = 1,
    val analytics: ProductAnalytics = ProductAnalytics()
)

/**
 * Product dimensions
 */
@Serializable
data class ProductDimensions(
    val length: Double = 0.0,
    val width: Double = 0.0,
    val height: Double = 0.0,
    val unit: String = "cm" // cm, inch, etc.
)

/**
 * Product SEO data
 */
@Serializable
data class ProductSEO(
    val metaTitle: String = "",
    val metaDescription: String = "",
    val keywords: List<String> = emptyList(),
    val slug: String = "",
    val canonicalUrl: String = ""
)

/**
 * Product analytics data
 */
@Serializable
data class ProductAnalytics(
    val viewCount: Int = 0,
    val orderCount: Int = 0,
    val favoriteCount: Int = 0,
    val shareCount: Int = 0,
    val conversionRate: Double = 0.0,
    val averageRating: Double = 0.0,
    val reviewCount: Int = 0,
    val lastViewed: Long = 0L,
    val lastOrdered: Long = 0L
)

/**
 * Product search filters
 */
@Serializable
data class ProductSearchFilters(
    val categories: List<String> = emptyList(),
    val genres: List<String> = emptyList(),
    val brands: List<String> = emptyList(),
    val suppliers: List<String> = emptyList(),
    val tags: List<String> = emptyList(),
    val priceRange: PriceRange? = null,
    val stockRange: StockRange? = null,
    val isActive: Boolean? = null,
    val isAvailable: Boolean? = null,
    val isFeatured: Boolean? = null,
    val hasImages: Boolean? = null,
    val dateRange: DateRange? = null,
    val searchQuery: String = "",
    val sortBy: ProductSortBy = ProductSortBy.NAME,
    val sortOrder: SortOrder = SortOrder.ASC
)

/**
 * Price range for filtering
 */
@Serializable
data class PriceRange(
    val minPrice: Double,
    val maxPrice: Double,
    val currency: String = "USD"
)

/**
 * Stock range for filtering
 */
@Serializable
data class StockRange(
    val minStock: Int,
    val maxStock: Int
)

/**
 * Product sorting options
 */
@Serializable
enum class ProductSortBy {
    NAME,
    PRICE,
    STOCK,
    CREATED_DATE,
    UPDATED_DATE,
    VIEW_COUNT,
    ORDER_COUNT,
    RATING
}

/**
 * Sort order
 */
@Serializable
enum class SortOrder {
    ASC,
    DESC
}

/**
 * Product creation request
 */
@Serializable
data class ProductCreateRequest(
    val name: String,
    val description: String = "",
    val sku: String = "",
    val category: String,
    val genre: String = "",
    val brand: String = "",
    val supplier: String = "",
    val price: Double,
    val cost: Double = 0.0,
    val currency: String = "USD",
    val weight: Double = 0.0,
    val dimensions: ProductDimensions = ProductDimensions(),
    val stock: Int = 0,
    val minStock: Int = 0,
    val maxStock: Int = 0,
    val tags: List<String> = emptyList(),
    val specifications: Map<String, String> = emptyMap(),
    val seoData: ProductSEO = ProductSEO()
)

/**
 * Product update request
 */
@Serializable
data class ProductUpdateRequest(
    val id: String,
    val name: String? = null,
    val description: String? = null,
    val sku: String? = null,
    val category: String? = null,
    val genre: String? = null,
    val brand: String? = null,
    val supplier: String? = null,
    val price: Double? = null,
    val cost: Double? = null,
    val currency: String? = null,
    val weight: Double? = null,
    val dimensions: ProductDimensions? = null,
    val stock: Int? = null,
    val minStock: Int? = null,
    val maxStock: Int? = null,
    val isActive: Boolean? = null,
    val isAvailable: Boolean? = null,
    val isFeatured: Boolean? = null,
    val tags: List<String>? = null,
    val specifications: Map<String, String>? = null,
    val seoData: ProductSEO? = null
)

/**
 * Product statistics for dashboard
 */
@Serializable
data class ProductStats(
    val totalProducts: Int = 0,
    val activeProducts: Int = 0,
    val availableProducts: Int = 0,
    val featuredProducts: Int = 0,
    val lowStockProducts: Int = 0,
    val outOfStockProducts: Int = 0,
    val productsByCategory: Map<String, Int> = emptyMap(),
    val productsByGenre: Map<String, Int> = emptyMap(),
    val productsByBrand: Map<String, Int> = emptyMap(),
    val averagePrice: Double = 0.0,
    val totalValue: Double = 0.0,
    val topSellingProducts: List<ProductSummary> = emptyList(),
    val recentlyAdded: List<ProductSummary> = emptyList()
)

/**
 * Product summary for lists and analytics
 */
@Serializable
data class ProductSummary(
    val id: String,
    val name: String,
    val sku: String,
    val price: Double,
    val currency: String,
    val stock: Int,
    val primaryImageUrl: String = "",
    val category: String,
    val genre: String,
    val isActive: Boolean,
    val isAvailable: Boolean,
    val viewCount: Int = 0,
    val orderCount: Int = 0
)
