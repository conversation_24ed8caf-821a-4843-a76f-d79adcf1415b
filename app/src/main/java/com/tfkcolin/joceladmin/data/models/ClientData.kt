package com.tfkcolin.joceladmin.data.models

import kotlinx.serialization.Serializable

/**
 * Client/Customer data model
 * Contains customer information for commands
 */
@Serializable
data class ClientData(
    val name: String = "",
    val tel: String = "",
    val email: String = "",
    val country: String = "",
    val city: String = "",
    val address: String = "",
    val notes: String = ""
) {
    /**
     * Validate client data
     */
    fun isValid(): <PERSON><PERSON><PERSON> {
        return name.isNotBlank() && tel.isNotBlank() && country.isNotBlank()
    }

    /**
     * Get display name for client
     */
    fun getDisplayName(): String {
        return if (name.isNotBlank()) name else "Unknown Client"
    }

    /**
     * Get formatted location
     */
    fun getFormattedLocation(): String {
        return buildString {
            if (city.isNotBlank()) {
                append(city)
                if (country.isNotBlank()) append(", ")
            }
            if (country.isNotBlank()) {
                append(country)
            }
        }.ifBlank { "Location not specified" }
    }

    /**
     * Get contact information
     */
    fun getContactInfo(): String {
        return buildString {
            if (tel.isNotBlank()) {
                append("Tel: $tel")
            }
            if (email.isNotBlank()) {
                if (tel.isNotBlank()) append(" | ")
                append("Email: $email")
            }
        }.ifBlank { "No contact information" }
    }
}
