package com.tfkcolin.joceladmin.data.models

import kotlinx.serialization.Serializable

/**
 * Cargo data model representing shipping containers
 * Implements cargo workflow: Loading → In Transit → Arrived
 */
@Serializable
data class Cargo(
    val id: String = "",
    val origin: String = "",
    val destination: String = "",
    val statusIndex: Int = 0, // Maps to CargoStatus ordinal
    val departureDate: Long? = null,
    val estimatedArrivalDate: Long? = null,
    val actualArrivalDate: Long? = null,
    val searchIndex: String = "", // For better search functionality
    val created: Long = java.util.Calendar.getInstance().timeInMillis,
    val updatedAt: Long = System.currentTimeMillis(),
    val containerNumber: String = "",
    val shipmentIds: List<String> = emptyList(), // List of shipment IDs
    val totalWeightKg: Double = 0.0,
    val totalVolumeCbm: Double = 0.0,
    val maxWeightKg: Double = 25000.0, // Default container capacity
    val maxVolumeCbm: Double = 67.0, // Default container capacity
    val createdBy: String = "",
    val notes: String = "",
    val route: String = "",
    val carrier: String = "",
    val vesselName: String = "",
    val currentLocation: String = ""
) {
    /**
     * Get current cargo status
     */
    fun getStatus(): CargoStatus {
        return CargoStatus.fromIndex(statusIndex)
    }

    /**
     * Update cargo status
     */
    fun updateStatus(status: CargoStatus): Cargo {
        return copy(
            statusIndex = status.ordinal,
            updatedAt = System.currentTimeMillis()
        )
    }

    /**
     * Get formatted container number
     */
    fun getFormattedContainerNumber(): String {
        return if (containerNumber.isNotBlank()) containerNumber else "CONT-${id.take(8)}"
    }

    /**
     * Check if cargo has capacity for additional shipment
     */
    fun hasCapacityFor(weightKg: Double, volumeCbm: Double): Boolean {
        return (totalWeightKg + weightKg <= maxWeightKg) &&
               (totalVolumeCbm + volumeCbm <= maxVolumeCbm)
    }

    /**
     * Calculate weight utilization percentage
     */
    fun getWeightUtilization(): Double {
        return if (maxWeightKg > 0) (totalWeightKg / maxWeightKg) * 100 else 0.0
    }

    /**
     * Calculate volume utilization percentage
     */
    fun getVolumeUtilization(): Double {
        return if (maxVolumeCbm > 0) (totalVolumeCbm / maxVolumeCbm) * 100 else 0.0
    }

    /**
     * Check if cargo is full
     */
    fun isFull(): Boolean {
        return getWeightUtilization() >= 100.0 || getVolumeUtilization() >= 100.0
    }

    /**
     * Check if cargo can accept new shipments
     */
    fun canAcceptShipments(): Boolean {
        return getStatus() == CargoStatus.LOADING && !isFull()
    }

    /**
     * Validate cargo data
     */
    fun isValid(): Boolean {
        return origin.isNotBlank() &&
               destination.isNotBlank() &&
               maxWeightKg > 0 &&
               maxVolumeCbm > 0
    }

    /**
     * Get cargo summary for display
     */
    fun getSummary(): String {
        return "${getFormattedContainerNumber()} - $origin to $destination"
    }

    /**
     * Check if cargo can progress to next status
     */
    fun canProgress(): Boolean {
        return getStatus().canProgress() && isValid()
    }

    /**
     * Check if cargo can regress to previous status
     */
    fun canRegress(): Boolean {
        return getStatus().canRegress()
    }

    /**
     * Get route description
     */
    fun getRouteDescription(): String {
        return if (route.isNotBlank()) route else "$origin → $destination"
    }
}

/**
 * Shipment data model representing individual customer shipments within cargos
 */
@Serializable
data class Shipment(
    val id: String = "",
    val cargoId: String = "",
    val clientName: String = "",
    val clientPhone: String = "",
    val clientEmail: String = "",
    val weightKg: Double = 0.0,
    val volumeCbm: Double = 0.0,
    val amountDue: Double = 0.0,
    val currency: String = "USD",
    val statusIndex: Int = 0, // Maps to ShipmentStatus ordinal
    val products: List<ShipmentProduct> = emptyList(),
    val created: Long = java.util.Calendar.getInstance().timeInMillis,
    val updatedAt: Long = System.currentTimeMillis(),
    val deliveryAddress: String = "",
    val trackingNumber: String = "",
    val notes: String = "",
    val estimatedDeliveryDate: Long? = null,
    val actualDeliveryDate: Long? = null
) {
    /**
     * Get current shipment status
     */
    fun getStatus(): ShipmentStatus {
        return ShipmentStatus.fromIndex(statusIndex)
    }

    /**
     * Update shipment status
     */
    fun updateStatus(status: ShipmentStatus): Shipment {
        return copy(
            statusIndex = status.ordinal,
            updatedAt = System.currentTimeMillis()
        )
    }

    /**
     * Calculate total weight of products
     */
    fun calculateTotalWeight(): Double {
        return products.sumOf { it.weightKg * it.quantity }
    }

    /**
     * Calculate total volume of products
     */
    fun calculateTotalVolume(): Double {
        return products.sumOf { it.volumeCbm * it.quantity }
    }

    /**
     * Get formatted amount due
     */
    fun getFormattedAmountDue(): String {
        return "$currency ${String.format("%.2f", amountDue)}"
    }

    /**
     * Get formatted tracking number
     */
    fun getFormattedTrackingNumber(): String {
        return if (trackingNumber.isNotBlank()) trackingNumber else "SHP-${id.take(8)}"
    }

    /**
     * Check if shipment is delivered
     */
    fun isDelivered(): Boolean {
        return getStatus() == ShipmentStatus.DELIVERED
    }

    /**
     * Check if shipment can be assigned to cargo
     */
    fun canAssignToCargo(): Boolean {
        return getStatus() == ShipmentStatus.PENDING
    }

    /**
     * Validate shipment data
     */
    fun isValid(): Boolean {
        return clientName.isNotBlank() &&
               clientPhone.isNotBlank() &&
               products.isNotEmpty() &&
               weightKg >= 0 &&
               volumeCbm >= 0 &&
               amountDue >= 0
    }

    /**
     * Get client contact information
     */
    fun getClientContact(): String {
        return buildString {
            append(clientName)
            if (clientPhone.isNotBlank()) {
                append(" - $clientPhone")
            }
            if (clientEmail.isNotBlank()) {
                append(" ($clientEmail)")
            }
        }
    }

    /**
     * Get shipment summary for display
     */
    fun getSummary(): String {
        return "${getFormattedTrackingNumber()} - $clientName"
    }

    /**
     * Check if shipment can progress to next status
     */
    fun canProgress(): Boolean {
        return getStatus().canProgress() && isValid()
    }

    /**
     * Check if shipment can regress to previous status
     */
    fun canRegress(): Boolean {
        return getStatus().canRegress()
    }
}

/**
 * Product information within a shipment
 */
@Serializable
data class ShipmentProduct(
    val name: String = "",
    val quantity: Int = 0,
    val description: String = "",
    val weightKg: Double = 0.0,
    val volumeCbm: Double = 0.0,
    val value: Double = 0.0,
    val category: String = ""
) {
    /**
     * Calculate total weight
     */
    fun getTotalWeight(): Double {
        return weightKg * quantity
    }

    /**
     * Calculate total volume
     */
    fun getTotalVolume(): Double {
        return volumeCbm * quantity
    }

    /**
     * Calculate total value
     */
    fun getTotalValue(): Double {
        return value * quantity
    }

    /**
     * Validate product data
     */
    fun isValid(): Boolean {
        return name.isNotBlank() &&
               quantity > 0 &&
               weightKg >= 0 &&
               volumeCbm >= 0 &&
               value >= 0
    }

    /**
     * Get product summary
     */
    fun getSummary(): String {
        return "$name (Qty: $quantity)"
    }
}

/**
 * Location data for origin/destination tracking
 */
@Serializable
data class Location(
    val name: String = "",
    val address: String = "",
    val city: String = "",
    val country: String = "",
    val postalCode: String = "",
    val latitude: Double? = null,
    val longitude: Double? = null,
    val port: String? = null,
    val airport: String? = null
)



/**
 * Cargo statistics for dashboard
 */
@Serializable
data class CargoStats(
    val totalCargos: Int = 0,
    val cargosByStatus: Map<CargoStatus, Int> = emptyMap(),
    val totalShipments: Int = 0,
    val shipmentsByStatus: Map<ShipmentStatus, Int> = emptyMap(),
    val totalWeight: Double = 0.0,
    val totalVolume: Double = 0.0,
    val averageDeliveryTime: Double = 0.0
)
