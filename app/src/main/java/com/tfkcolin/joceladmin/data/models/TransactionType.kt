package com.tfkcolin.joceladmin.data.models

/**
 * Transaction type enumeration
 * Represents income and expense transaction types
 */
enum class TransactionType(
    val label: String,
    val description: String
) {
    INPUT(
        label = "Income",
        description = "Revenue and income transactions"
    ),
    OUTPUT(
        label = "Expense", 
        description = "Costs and expense transactions"
    );

    /**
     * Check if transaction type is income
     */
    fun isIncome(): <PERSON>olean {
        return this == INPUT
    }

    /**
     * Check if transaction type is expense
     */
    fun isExpense(): Boolean {
        return this == OUTPUT
    }

    companion object {
        /**
         * Get transaction type by ordinal index
         */
        fun fromIndex(index: Int): TransactionType {
            return values().getOrNull(index) ?: INPUT
        }
    }
}
