package com.tfkcolin.joceladmin.data.models

/**
 * Cargo status enumeration
 * Represents the 3-stage cargo workflow: Loading → In Transit → Arrived
 */
enum class CargoStatus(
    val label: String,
    val description: String
) {
    LOADING(
        label = "Loading",
        description = "Cargo container being loaded with shipments"
    ),
    IN_TRANSIT(
        label = "In Transit",
        description = "Cargo en route to destination"
    ),
    ARRIVED(
        label = "Arrived",
        description = "Cargo arrived at destination"
    );

    /**
     * Get next status in workflow
     */
    fun getNext(): CargoStatus? {
        val currentIndex = values().indexOf(this)
        return if (currentIndex < values().size - 1) {
            values()[currentIndex + 1]
        } else null
    }

    /**
     * Get previous status in workflow
     */
    fun getPrevious(): CargoStatus? {
        val currentIndex = values().indexOf(this)
        return if (currentIndex > 0) {
            values()[currentIndex - 1]
        } else null
    }

    /**
     * Check if status can progress to next
     */
    fun canProgress(): <PERSON><PERSON>an {
        return this != ARRIVED
    }

    /**
     * Check if status can regress to previous
     */
    fun canRegress(): Boolean {
        return this != LOADING
    }

    companion object {
        /**
         * Get status by ordinal index
         */
        fun fromIndex(index: Int): CargoStatus {
            return values().getOrNull(index) ?: LOADING
        }
    }
}
