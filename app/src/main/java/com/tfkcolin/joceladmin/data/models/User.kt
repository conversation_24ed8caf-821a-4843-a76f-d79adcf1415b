package com.tfkcolin.joceladmin.data.models

import kotlinx.serialization.Serializable

/**
 * User data model representing a user in the system
 * Based on the documentation's user management requirements
 */
@Serializable
data class User(
    val id: String = "",
    val email: String = "",
    val displayName: String = "",
    val photoUrl: String? = null,
    val role: UserRole = UserRole.EMPLOYEE,
    val isActive: Boolean = true,
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis(),
    val lastLoginAt: Long? = null,
    val permissions: List<String> = emptyList()
)

/**
 * User roles as defined in the documentation
 */
@Serializable
enum class UserRole(val displayName: String) {
    ADMIN("Admin"),
    EMPLOYEE("Employee"),
    DELIVERER("Deliverer")
}

/**
 * User permissions for fine-grained access control
 */
object UserPermissions {
    const val VIEW_COMMANDS = "view_commands"
    const val EDIT_COMMANDS = "edit_commands"
    const val DELETE_COMMANDS = "delete_commands"
    const val VIEW_FINANCIAL = "view_financial"
    const val EDIT_FINANCIAL = "edit_financial"
    const val VIEW_CARGO = "view_cargo"
    const val EDIT_CARGO = "edit_cargo"
    const val MANAGE_USERS = "manage_users"
    const val VIEW_ANALYTICS = "view_analytics"
    
    /**
     * Get default permissions for a role
     */
    fun getDefaultPermissions(role: UserRole): List<String> {
        return when (role) {
            UserRole.ADMIN -> listOf(
                VIEW_COMMANDS, EDIT_COMMANDS, DELETE_COMMANDS,
                VIEW_FINANCIAL, EDIT_FINANCIAL,
                VIEW_CARGO, EDIT_CARGO,
                MANAGE_USERS, VIEW_ANALYTICS
            )
            UserRole.EMPLOYEE -> listOf(
                VIEW_COMMANDS, EDIT_COMMANDS,
                VIEW_CARGO, EDIT_CARGO,
                VIEW_ANALYTICS
            )
            UserRole.DELIVERER -> listOf(
                VIEW_COMMANDS, VIEW_CARGO
            )
        }
    }
}
