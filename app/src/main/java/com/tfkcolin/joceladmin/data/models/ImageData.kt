package com.tfkcolin.joceladmin.data.models

import kotlinx.serialization.Serializable

/**
 * Image data model for product catalog management
 */
@Serializable
data class ImageData(
    val id: String = "",
    val filename: String = "",
    val originalUrl: String = "",
    val thumbnailUrl: String = "",
    val compressedUrl: String = "",
    val category: String = "",
    val genre: String = "",
    val tags: List<String> = emptyList(),
    val description: String = "",
    val altText: String = "",
    val fileSize: Long = 0L,
    val width: Int = 0,
    val height: Int = 0,
    val format: String = "", // jpg, png, webp, etc.
    val uploadedAt: Long = System.currentTimeMillis(),
    val uploadedBy: String = "",
    val isActive: Boolean = true,
    val downloadCount: Int = 0,
    val metadata: ImageMetadata = ImageMetadata(),
    val productInfo: ProductInfo? = null
)

/**
 * Image metadata for technical information
 */
@Serializable
data class ImageMetadata(
    val exifData: Map<String, String> = emptyMap(),
    val colorProfile: String = "",
    val compression: String = "",
    val dpi: Int = 0,
    val hasTransparency: Boolean = false,
    val dominantColors: List<String> = emptyList(),
    val processingHistory: List<String> = emptyList()
)

/**
 * Product information associated with an image
 */
@Serializable
data class ProductInfo(
    val name: String = "",
    val sku: String = "",
    val price: Double = 0.0,
    val currency: String = "USD",
    val availability: Boolean = true,
    val stock: Int = 0,
    val supplier: String = "",
    val specifications: Map<String, String> = emptyMap(),
    val variants: List<ProductVariant> = emptyList()
)

/**
 * Product variant information
 */
@Serializable
data class ProductVariant(
    val id: String = "",
    val name: String = "",
    val value: String = "",
    val price: Double = 0.0,
    val stock: Int = 0,
    val imageUrl: String? = null
)

/**
 * Image category for organization
 */
@Serializable
data class ImageCategory(
    val id: String = "",
    val name: String = "",
    val description: String = "",
    val parentId: String? = null,
    val isActive: Boolean = true,
    val sortOrder: Int = 0,
    val imageCount: Int = 0,
    val genres: List<ImageGenre> = emptyList()
)

/**
 * Image genre for sub-categorization
 */
@Serializable
data class ImageGenre(
    val id: String = "",
    val name: String = "",
    val description: String = "",
    val categoryId: String = "",
    val isActive: Boolean = true,
    val sortOrder: Int = 0,
    val imageCount: Int = 0
)

/**
 * Image search filters
 */
@Serializable
data class ImageSearchFilters(
    val categories: List<String> = emptyList(),
    val genres: List<String> = emptyList(),
    val tags: List<String> = emptyList(),
    val dateRange: DateRange? = null,
    val sizeRange: SizeRange? = null,
    val format: String? = null,
    val hasProductInfo: Boolean? = null,
    val isActive: Boolean? = null,
    val uploadedBy: String? = null,
    val searchQuery: String = ""
)

/**
 * Date range for filtering
 */
@Serializable
data class DateRange(
    val startDate: Long,
    val endDate: Long
)

/**
 * Size range for filtering
 */
@Serializable
data class SizeRange(
    val minWidth: Int? = null,
    val maxWidth: Int? = null,
    val minHeight: Int? = null,
    val maxHeight: Int? = null,
    val minFileSize: Long? = null,
    val maxFileSize: Long? = null
)

/**
 * Image upload request
 */
@Serializable
data class ImageUploadRequest(
    val filename: String,
    val category: String,
    val genre: String = "",
    val tags: List<String> = emptyList(),
    val description: String = "",
    val altText: String = "",
    val productInfo: ProductInfo? = null,
    val generateThumbnail: Boolean = true,
    val generateCompressed: Boolean = true,
    val maxWidth: Int = 1920,
    val maxHeight: Int = 1080,
    val quality: Int = 85
)

/**
 * Image processing result
 */
@Serializable
data class ImageProcessingResult(
    val originalUrl: String,
    val thumbnailUrl: String? = null,
    val compressedUrl: String? = null,
    val metadata: ImageMetadata,
    val processingTime: Long = 0L,
    val success: Boolean = true,
    val error: String? = null
)

/**
 * Image statistics for dashboard
 */
@Serializable
data class ImageStats(
    val totalImages: Int = 0,
    val imagesByCategory: Map<String, Int> = emptyMap(),
    val imagesByGenre: Map<String, Int> = emptyMap(),
    val totalStorageUsed: Long = 0L,
    val averageFileSize: Long = 0L,
    val mostPopularTags: List<TagSummary> = emptyList(),
    val uploadTrend: List<UploadData> = emptyList(),
    val formatDistribution: Map<String, Int> = emptyMap()
)

/**
 * Tag summary for analytics
 */
@Serializable
data class TagSummary(
    val tag: String,
    val count: Int,
    val percentage: Double
)

/**
 * Upload data for trends
 */
@Serializable
data class UploadData(
    val date: String, // e.g., "2024-12-01"
    val count: Int,
    val totalSize: Long
)
