package com.tfkcolin.joceladmin.data.models

import kotlinx.serialization.Serializable

/**
 * Country data model
 * Contains country configuration for multi-country operations
 */
@Serializable
data class CountryData(
    val id: String = "",
    val name: String = "",
    val devise: String = "", // Currency code
    val currencySymbol: String = "",
    val isActive: Boolean = true,
    val taxRate: Double = 0.0,
    val created: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis()
) {
    /**
     * Validate country data
     */
    fun isValid(): Boolean {
        return name.isNotBlank() && devise.isNotBlank()
    }

    /**
     * Get formatted currency display
     */
    fun getFormattedCurrency(): String {
        return if (currencySymbol.isNotBlank()) {
            "$currencySymbol ($devise)"
        } else {
            devise
        }
    }

    /**
     * Get display name with currency
     */
    fun getDisplayName(): String {
        return "$name (${getFormattedCurrency()})"
    }

    /**
     * Format amount with currency
     */
    fun formatAmount(amount: Double): String {
        val symbol = currencySymbol.ifBlank { devise }
        return "$symbol ${String.format("%.2f", amount)}"
    }

    /**
     * Calculate tax amount
     */
    fun calculateTax(amount: Double): Double {
        return amount * (taxRate / 100)
    }

    /**
     * Calculate amount with tax
     */
    fun calculateAmountWithTax(amount: Double): Double {
        return amount + calculateTax(amount)
    }
}
