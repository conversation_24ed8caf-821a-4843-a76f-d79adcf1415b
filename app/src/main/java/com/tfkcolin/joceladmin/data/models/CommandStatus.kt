package com.tfkcolin.joceladmin.data.models

/**
 * Command workflow status enumeration
 * Represents the 6-stage workflow: Record → Buying → Received → Delivered → Ready → OK
 */
enum class CommandStatus(
    val step: String,
    val label: String,
    val description: String,
    val iconName: String
) {
    RECORD(
        step = "RECORD",
        label = "Enregistrement",
        description = "Initial order entry and customer data capture",
        iconName = "ic_record_24"
    ),
    BUYING(
        step = "BUYING",
        label = "Commander",
        description = "Product procurement and sourcing",
        iconName = "ic_buying_24"
    ),
    RECEIVED(
        step = "RECEIVED",
        label = "Reçue",
        description = "Product receipt and quality verification",
        iconName = "ic_received_24"
    ),
    DELIVERED(
        step = "DELIVERED",
        label = "Expédié",
        description = "Product shipment to destination",
        iconName = "ic_delivery_boat_24"
    ),
    READY(
        step = "READY",
        label = "Prêt",
        description = "Product ready for customer pickup/delivery",
        iconName = "ic_home_24"
    ),
    OK(
        step = "OK",
        label = "Livré",
        description = "Order completion and customer satisfaction",
        iconName = "ic_ok_24"
    );

    /**
     * Get next status in workflow
     */
    fun getNext(): CommandStatus? {
        val currentIndex = values().indexOf(this)
        return if (currentIndex < values().size - 1) {
            values()[currentIndex + 1]
        } else null
    }

    /**
     * Get previous status in workflow
     */
    fun getPrevious(): CommandStatus? {
        val currentIndex = values().indexOf(this)
        return if (currentIndex > 0) {
            values()[currentIndex - 1]
        } else null
    }

    /**
     * Check if status can progress to next
     */
    fun canProgress(): Boolean {
        return this != OK
    }

    /**
     * Check if status can regress to previous
     */
    fun canRegress(): Boolean {
        return this != RECORD
    }
}

/**
 * Utility functions for CommandStatus
 */
object CommandStatusUtils {
    /**
     * Get status by step string
     */
    fun fromStep(step: String): CommandStatus {
        return CommandStatus.values().find { it.name == step } ?: CommandStatus.RECORD
    }
}
