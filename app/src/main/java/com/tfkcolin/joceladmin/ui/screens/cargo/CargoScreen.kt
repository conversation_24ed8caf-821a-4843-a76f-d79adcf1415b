package com.tfkcolin.joceladmin.ui.screens.cargo

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.FilterList
import androidx.compose.material.icons.filled.Search
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.tfkcolin.joceladmin.data.models.Cargo
import com.tfkcolin.joceladmin.data.models.CargoStatus

/**
 * Cargo list screen
 * Displays list of cargo containers with filtering and search capabilities
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CargoScreen(
    onNavigateToDetails: (String) -> Unit,
    onNavigateToCreate: () -> Unit,
    viewModel: CargoListViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    var showFilterDialog by remember { mutableStateOf(false) }
    var searchQuery by remember { mutableStateOf("") }

    LaunchedEffect(searchQuery) {
        viewModel.searchCargos(searchQuery)
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        // Header with title and actions
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "Cargo Containers",
                style = MaterialTheme.typography.headlineMedium,
                fontWeight = FontWeight.Bold
            )
            
            Row {
                IconButton(onClick = { showFilterDialog = true }) {
                    Icon(Icons.Default.FilterList, contentDescription = "Filter")
                }
                
                FloatingActionButton(
                    onClick = onNavigateToCreate,
                    modifier = Modifier.size(48.dp)
                ) {
                    Icon(Icons.Default.Add, contentDescription = "Add Cargo")
                }
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        // Search bar
        OutlinedTextField(
            value = searchQuery,
            onValueChange = { searchQuery = it },
            label = { Text("Search by route or container") },
            leadingIcon = { Icon(Icons.Default.Search, contentDescription = null) },
            modifier = Modifier.fillMaxWidth(),
            singleLine = true
        )

        Spacer(modifier = Modifier.height(16.dp))

        // Statistics cards
        if (uiState.statistics.isNotEmpty()) {
            CargoStatisticsRow(statistics = uiState.statistics)
            Spacer(modifier = Modifier.height(16.dp))
        }

        // Active filters display
        if (uiState.selectedStatus != null || uiState.selectedOrigin.isNotBlank() || uiState.selectedDestination.isNotBlank()) {
            ActiveFiltersRow(
                selectedStatus = uiState.selectedStatus,
                selectedOrigin = uiState.selectedOrigin,
                selectedDestination = uiState.selectedDestination,
                onClearFilters = viewModel::clearFilters
            )
            Spacer(modifier = Modifier.height(8.dp))
        }

        // Cargo list
        when {
            uiState.isLoading -> {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    CircularProgressIndicator()
                }
            }
            
            uiState.errorMessage != null -> {
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.errorContainer
                    )
                ) {
                    Column(
                        modifier = Modifier.padding(16.dp)
                    ) {
                        Text(
                            text = "Error",
                            style = MaterialTheme.typography.titleMedium,
                            color = MaterialTheme.colorScheme.onErrorContainer
                        )
                        Text(
                            text = uiState.errorMessage ?: "Unknown error",
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onErrorContainer
                        )
                        Spacer(modifier = Modifier.height(8.dp))
                        Button(
                            onClick = {
                                viewModel.clearError()
                                viewModel.refresh()
                            }
                        ) {
                            Text("Retry")
                        }
                    }
                }
            }
            
            uiState.cargos.isEmpty() -> {
                Card(
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(32.dp),
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Text(
                            text = "No cargo containers found",
                            style = MaterialTheme.typography.titleMedium
                        )
                        Text(
                            text = "Create your first cargo container to get started",
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                        Spacer(modifier = Modifier.height(16.dp))
                        Button(onClick = onNavigateToCreate) {
                            Text("Create Cargo")
                        }
                    }
                }
            }
            
            else -> {
                LazyColumn(
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    items(uiState.cargos) { cargo ->
                        CargoItem(
                            cargo = cargo,
                            onClick = { onNavigateToDetails(cargo.id) },
                            onStatusUpdate = { status ->
                                viewModel.updateCargoStatus(cargo.id, status)
                            }
                        )
                    }
                }
            }
        }
    }

    // Filter dialog
    if (showFilterDialog) {
        CargoFilterDialog(
            selectedStatus = uiState.selectedStatus,
            selectedOrigin = uiState.selectedOrigin,
            selectedDestination = uiState.selectedDestination,
            onStatusSelected = { status ->
                viewModel.filterByStatus(status)
                showFilterDialog = false
            },
            onOriginSelected = { origin ->
                viewModel.filterByOrigin(origin)
                showFilterDialog = false
            },
            onDestinationSelected = { destination ->
                viewModel.filterByDestination(destination)
                showFilterDialog = false
            },
            onDismiss = { showFilterDialog = false }
        )
    }
}

@Composable
private fun CargoStatisticsRow(
    statistics: Map<String, Int>
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        StatisticCard(
            title = "Total",
            value = statistics["total"] ?: 0,
            modifier = Modifier.weight(1f)
        )
        StatisticCard(
            title = "Loading",
            value = statistics["loading"] ?: 0,
            modifier = Modifier.weight(1f)
        )
        StatisticCard(
            title = "In Transit",
            value = statistics["inTransit"] ?: 0,
            modifier = Modifier.weight(1f)
        )
        StatisticCard(
            title = "Arrived",
            value = statistics["arrived"] ?: 0,
            modifier = Modifier.weight(1f)
        )
    }
}

@Composable
private fun StatisticCard(
    title: String,
    value: Int,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
    ) {
        Column(
            modifier = Modifier.padding(12.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = value.toString(),
                style = MaterialTheme.typography.headlineSmall,
                fontWeight = FontWeight.Bold
            )
            Text(
                text = title,
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}

@Composable
private fun ActiveFiltersRow(
    selectedStatus: CargoStatus?,
    selectedOrigin: String,
    selectedDestination: String,
    onClearFilters: () -> Unit
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.spacedBy(8.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = "Active filters:",
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
        
        if (selectedStatus != null) {
            FilterChip(
                selected = true,
                onClick = onClearFilters,
                label = { Text(selectedStatus.label) }
            )
        }
        
        if (selectedOrigin.isNotBlank()) {
            FilterChip(
                selected = true,
                onClick = onClearFilters,
                label = { Text("From: $selectedOrigin") }
            )
        }
        
        if (selectedDestination.isNotBlank()) {
            FilterChip(
                selected = true,
                onClick = onClearFilters,
                label = { Text("To: $selectedDestination") }
            )
        }
        
        TextButton(onClick = onClearFilters) {
            Text("Clear all")
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun CargoItem(
    cargo: Cargo,
    onClick: () -> Unit,
    onStatusUpdate: (CargoStatus) -> Unit
) {
    Card(
        onClick = onClick,
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // Header row with container number and status
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = cargo.getFormattedContainerNumber(),
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )

                CargoStatusChip(
                    status = cargo.getStatus(),
                    onClick = { /* TODO: Status update dialog */ }
                )
            }

            Spacer(modifier = Modifier.height(8.dp))

            // Route information
            Text(
                text = cargo.getRouteDescription(),
                style = MaterialTheme.typography.bodyLarge
            )

            if (cargo.carrier.isNotBlank()) {
                Text(
                    text = "Carrier: ${cargo.carrier}",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }

            Spacer(modifier = Modifier.height(8.dp))

            // Capacity information
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Column {
                    Text(
                        text = "Weight: ${String.format("%.1f", cargo.getWeightUtilization())}%",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    Text(
                        text = "${cargo.totalWeightKg}/${cargo.maxWeightKg} kg",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }

                Column {
                    Text(
                        text = "Volume: ${String.format("%.1f", cargo.getVolumeUtilization())}%",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    Text(
                        text = "${cargo.totalVolumeCbm}/${cargo.maxVolumeCbm} m³",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }

                Text(
                    text = "${cargo.shipmentIds.size} shipments",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.primary
                )
            }
        }
    }
}

@Composable
private fun CargoStatusChip(
    status: CargoStatus,
    onClick: () -> Unit
) {
    FilterChip(
        selected = true,
        onClick = onClick,
        label = { Text(status.label) },
        colors = FilterChipDefaults.filterChipColors(
            selectedContainerColor = when (status) {
                CargoStatus.LOADING -> MaterialTheme.colorScheme.primaryContainer
                CargoStatus.IN_TRANSIT -> MaterialTheme.colorScheme.secondaryContainer
                CargoStatus.ARRIVED -> MaterialTheme.colorScheme.tertiaryContainer
            }
        )
    )
}

@Composable
private fun CargoFilterDialog(
    selectedStatus: CargoStatus?,
    selectedOrigin: String,
    selectedDestination: String,
    onStatusSelected: (CargoStatus?) -> Unit,
    onOriginSelected: (String) -> Unit,
    onDestinationSelected: (String) -> Unit,
    onDismiss: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("Filter Cargo Containers") },
        text = {
            Column {
                Text(
                    text = "Filter by Status",
                    style = MaterialTheme.typography.titleSmall,
                    modifier = Modifier.padding(bottom = 8.dp)
                )

                // Status filter options
                CargoStatus.values().forEach { status ->
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        RadioButton(
                            selected = selectedStatus == status,
                            onClick = { onStatusSelected(status) }
                        )
                        Text(
                            text = status.label,
                            modifier = Modifier.padding(start = 8.dp)
                        )
                    }
                }

                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    RadioButton(
                        selected = selectedStatus == null,
                        onClick = { onStatusSelected(null) }
                    )
                    Text(
                        text = "All Statuses",
                        modifier = Modifier.padding(start = 8.dp)
                    )
                }
            }
        },
        confirmButton = {
            TextButton(onClick = onDismiss) {
                Text("Close")
            }
        }
    )
}
