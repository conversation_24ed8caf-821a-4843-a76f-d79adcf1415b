package com.tfkcolin.joceladmin.ui.screens.command

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.tfkcolin.joceladmin.data.models.Command
import com.tfkcolin.joceladmin.data.models.CommandStatus
import com.tfkcolin.joceladmin.repository.CommandRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * ViewModel for command list screen
 * Handles command list management, filtering, and search
 */
@HiltViewModel
class CommandListViewModel @Inject constructor(
    private val commandRepository: CommandRepository
) : ViewModel() {

    private val _uiState = MutableStateFlow(CommandListUiState())
    val uiState: StateFlow<CommandListUiState> = _uiState.asStateFlow()

    init {
        loadCommands()
        loadStatistics()
    }

    /**
     * Load commands based on current filters
     */
    fun loadCommands() {
        _uiState.value = _uiState.value.copy(isLoading = true, errorMessage = null)

        viewModelScope.launch {
            val result = when {
                _uiState.value.selectedStatus != null -> {
                    commandRepository.getCommandsByStatus(_uiState.value.selectedStatus!!)
                }
                _uiState.value.selectedCountry.isNotBlank() -> {
                    commandRepository.getCommandsByCountry(_uiState.value.selectedCountry)
                }
                _uiState.value.searchQuery.isNotBlank() -> {
                    commandRepository.searchCommandsByClient(_uiState.value.searchQuery)
                }
                else -> {
                    commandRepository.getRecentCommands(50)
                }
            }

            result.fold(
                onSuccess = { commands ->
                    _uiState.value = _uiState.value.copy(
                        commands = commands,
                        isLoading = false,
                        errorMessage = null
                    )
                },
                onFailure = { exception ->
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        errorMessage = exception.message ?: "Failed to load commands"
                    )
                }
            )
        }
    }

    /**
     * Load command statistics
     */
    private fun loadStatistics() {
        viewModelScope.launch {
            commandRepository.getCommandStatistics().fold(
                onSuccess = { stats ->
                    _uiState.value = _uiState.value.copy(statistics = stats)
                },
                onFailure = { /* Ignore statistics errors */ }
            )
        }
    }

    /**
     * Filter commands by status
     */
    fun filterByStatus(status: CommandStatus?) {
        _uiState.value = _uiState.value.copy(
            selectedStatus = status,
            selectedCountry = "",
            searchQuery = ""
        )
        loadCommands()
    }

    /**
     * Filter commands by country
     */
    fun filterByCountry(country: String) {
        _uiState.value = _uiState.value.copy(
            selectedCountry = country,
            selectedStatus = null,
            searchQuery = ""
        )
        loadCommands()
    }

    /**
     * Search commands by client name
     */
    fun searchCommands(query: String) {
        _uiState.value = _uiState.value.copy(
            searchQuery = query,
            selectedStatus = null,
            selectedCountry = ""
        )
        if (query.length >= 2) { // Only search with 2+ characters
            loadCommands()
        } else if (query.isEmpty()) {
            loadCommands() // Load all when search is cleared
        }
    }

    /**
     * Clear all filters
     */
    fun clearFilters() {
        _uiState.value = _uiState.value.copy(
            selectedStatus = null,
            selectedCountry = "",
            searchQuery = ""
        )
        loadCommands()
    }

    /**
     * Refresh commands
     */
    fun refresh() {
        loadCommands()
        loadStatistics()
    }

    /**
     * Update command status
     */
    fun updateCommandStatus(commandId: String, status: CommandStatus) {
        viewModelScope.launch {
            commandRepository.updateCommandStatus(commandId, status).fold(
                onSuccess = {
                    loadCommands() // Refresh list
                },
                onFailure = { exception ->
                    _uiState.value = _uiState.value.copy(
                        errorMessage = "Failed to update status: ${exception.message}"
                    )
                }
            )
        }
    }

    /**
     * Archive command
     */
    fun archiveCommand(commandId: String) {
        viewModelScope.launch {
            commandRepository.archiveCommand(commandId).fold(
                onSuccess = {
                    loadCommands() // Refresh list
                },
                onFailure = { exception ->
                    _uiState.value = _uiState.value.copy(
                        errorMessage = "Failed to archive command: ${exception.message}"
                    )
                }
            )
        }
    }

    /**
     * Clear error message
     */
    fun clearError() {
        _uiState.value = _uiState.value.copy(errorMessage = null)
    }

    /**
     * Get commands requiring payment proof
     */
    fun loadCommandsRequiringPayment() {
        viewModelScope.launch {
            commandRepository.getCommandsRequiringPaymentProof().fold(
                onSuccess = { commands ->
                    _uiState.value = _uiState.value.copy(
                        commandsRequiringPayment = commands
                    )
                },
                onFailure = { /* Ignore errors for this optional feature */ }
            )
        }
    }
}

/**
 * UI state for command list screen
 */
data class CommandListUiState(
    val commands: List<Command> = emptyList(),
    val commandsRequiringPayment: List<Command> = emptyList(),
    val isLoading: Boolean = false,
    val errorMessage: String? = null,
    val selectedStatus: CommandStatus? = null,
    val selectedCountry: String = "",
    val searchQuery: String = "",
    val statistics: Map<String, Int> = emptyMap()
)
