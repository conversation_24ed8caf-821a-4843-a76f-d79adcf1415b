package com.tfkcolin.joceladmin.ui.screens.command

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Edit
import androidx.compose.material.icons.filled.Save
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.tfkcolin.joceladmin.data.models.ClientData
import com.tfkcolin.joceladmin.data.models.MiniProduct

/**
 * Command details screen
 * Displays and allows editing of individual command details
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CommandDetailsScreen(
    commandId: String,
    onNavigateBack: () -> Unit,
    viewModel: CommandDetailsViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    var showAddProductDialog by remember { mutableStateOf(false) }

    LaunchedEffect(commandId) {
        viewModel.loadCommand(commandId)
    }

    LaunchedEffect(uiState.saveSuccessful) {
        if (uiState.saveSuccessful) {
            viewModel.clearSaveSuccess()
            if (uiState.isNewCommand) {
                onNavigateBack()
            }
        }
    }

    Column(
        modifier = Modifier.fillMaxSize()
    ) {
        // Top app bar
        TopAppBar(
            title = { 
                Text(
                    if (uiState.isNewCommand) "New Command" 
                    else uiState.command.getFormattedCommandNumber()
                )
            },
            navigationIcon = {
                IconButton(onClick = onNavigateBack) {
                    Icon(Icons.Default.ArrowBack, contentDescription = "Back")
                }
            },
            actions = {
                if (uiState.isEditMode) {
                    IconButton(
                        onClick = viewModel::saveCommand,
                        enabled = !uiState.isSaving
                    ) {
                        if (uiState.isSaving) {
                            CircularProgressIndicator(
                                modifier = Modifier.size(24.dp),
                                strokeWidth = 2.dp
                            )
                        } else {
                            Icon(Icons.Default.Save, contentDescription = "Save")
                        }
                    }
                } else {
                    IconButton(onClick = viewModel::toggleEditMode) {
                        Icon(Icons.Default.Edit, contentDescription = "Edit")
                    }
                }
            }
        )

        when {
            uiState.isLoading -> {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    CircularProgressIndicator()
                }
            }
            
            uiState.errorMessage != null -> {
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.errorContainer
                    )
                ) {
                    Column(
                        modifier = Modifier.padding(16.dp)
                    ) {
                        Text(
                            text = "Error",
                            style = MaterialTheme.typography.titleMedium,
                            color = MaterialTheme.colorScheme.onErrorContainer
                        )
                        Text(
                            text = uiState.errorMessage ?: "Unknown error",
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onErrorContainer
                        )
                        Spacer(modifier = Modifier.height(8.dp))
                        Button(onClick = viewModel::clearError) {
                            Text("Dismiss")
                        }
                    }
                }
            }
            
            else -> {
                LazyColumn(
                    modifier = Modifier.fillMaxSize(),
                    contentPadding = PaddingValues(16.dp),
                    verticalArrangement = Arrangement.spacedBy(16.dp)
                ) {
                    // Command status section
                    item {
                        CommandStatusSection(
                            command = uiState.command,
                            isEditMode = uiState.isEditMode,
                            onProgressStatus = viewModel::progressStatus,
                            onRegressStatus = viewModel::regressStatus
                        )
                    }

                    // Client information section
                    item {
                        ClientDataSection(
                            clientData = uiState.command.client,
                            isEditMode = uiState.isEditMode,
                            onClientDataUpdate = viewModel::updateClientData
                        )
                    }

                    // Products section header
                    item {
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceBetween,
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "Products (${uiState.command.products.size})",
                                style = MaterialTheme.typography.titleLarge,
                                fontWeight = FontWeight.Bold
                            )
                            
                            if (uiState.isEditMode) {
                                IconButton(onClick = { showAddProductDialog = true }) {
                                    Icon(Icons.Default.Add, contentDescription = "Add Product")
                                }
                            }
                        }
                    }

                    // Products list
                    if (uiState.command.products.isEmpty()) {
                        item {
                            Card(
                                modifier = Modifier.fillMaxWidth()
                            ) {
                                Column(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .padding(32.dp),
                                    horizontalAlignment = Alignment.CenterHorizontally
                                ) {
                                    Text(
                                        text = "No products added",
                                        style = MaterialTheme.typography.titleMedium
                                    )
                                    if (uiState.isEditMode) {
                                        Text(
                                            text = "Add products to this command",
                                            style = MaterialTheme.typography.bodyMedium,
                                            color = MaterialTheme.colorScheme.onSurfaceVariant
                                        )
                                        Spacer(modifier = Modifier.height(16.dp))
                                        Button(onClick = { showAddProductDialog = true }) {
                                            Text("Add Product")
                                        }
                                    }
                                }
                            }
                        }
                    } else {
                        itemsIndexed(uiState.command.products) { index, product ->
                            ProductItem(
                                product = product,
                                isEditMode = uiState.isEditMode,
                                onProductUpdate = { updatedProduct ->
                                    viewModel.updateProduct(index, updatedProduct)
                                },
                                onProductRemove = {
                                    viewModel.removeProduct(index)
                                }
                            )
                        }
                    }

                    // Financial summary section
                    item {
                        FinancialSummarySection(
                            command = uiState.command
                        )
                    }

                    // Observations section
                    item {
                        ObservationsSection(
                            observations = uiState.command.observation,
                            isEditMode = uiState.isEditMode,
                            onAddObservation = viewModel::addObservation
                        )
                    }

                    // Payment proof section
                    if (uiState.command.requiresPaymentProof() || uiState.command.proofUploaded) {
                        item {
                            PaymentProofSection(
                                command = uiState.command,
                                isEditMode = uiState.isEditMode,
                                onUpdatePaymentProof = viewModel::updatePaymentProof
                            )
                        }
                    }
                }
            }
        }
    }

    // Add product dialog
    if (showAddProductDialog) {
        AddProductDialog(
            onProductAdd = { product ->
                viewModel.addProduct(product)
                showAddProductDialog = false
            },
            onDismiss = { showAddProductDialog = false }
        )
    }
}

@Composable
private fun CommandStatusSection(
    command: com.tfkcolin.joceladmin.data.models.Command,
    isEditMode: Boolean,
    onProgressStatus: () -> Unit,
    onRegressStatus: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "Status",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )

            Spacer(modifier = Modifier.height(8.dp))

            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column {
                    Text(
                        text = command.getStatus().name,
                        style = MaterialTheme.typography.headlineSmall,
                        fontWeight = FontWeight.Bold
                    )
                    Text(
                        text = command.getStatus().description,
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }

                if (isEditMode) {
                    Row {
                        if (command.canRegress()) {
                            OutlinedButton(onClick = onRegressStatus) {
                                Text("Previous")
                            }
                        }

                        Spacer(modifier = Modifier.width(8.dp))

                        if (command.canProgress()) {
                            Button(onClick = onProgressStatus) {
                                Text("Next")
                            }
                        }
                    }
                }
            }
        }
    }
}

@Composable
private fun ClientDataSection(
    clientData: ClientData,
    isEditMode: Boolean,
    onClientDataUpdate: (ClientData) -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "Client Information",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )

            Spacer(modifier = Modifier.height(16.dp))

            if (isEditMode) {
                // Editable client form
                var name by remember { mutableStateOf(clientData.name) }
                var tel by remember { mutableStateOf(clientData.tel) }
                var email by remember { mutableStateOf(clientData.email) }
                var country by remember { mutableStateOf(clientData.country) }
                var city by remember { mutableStateOf(clientData.city) }

                LaunchedEffect(name, tel, email, country, city) {
                    onClientDataUpdate(
                        clientData.copy(
                            name = name,
                            tel = tel,
                            email = email,
                            country = country,
                            city = city
                        )
                    )
                }

                OutlinedTextField(
                    value = name,
                    onValueChange = { name = it },
                    label = { Text("Name *") },
                    modifier = Modifier.fillMaxWidth()
                )

                Spacer(modifier = Modifier.height(8.dp))

                OutlinedTextField(
                    value = tel,
                    onValueChange = { tel = it },
                    label = { Text("Phone *") },
                    modifier = Modifier.fillMaxWidth()
                )

                Spacer(modifier = Modifier.height(8.dp))

                OutlinedTextField(
                    value = email,
                    onValueChange = { email = it },
                    label = { Text("Email") },
                    modifier = Modifier.fillMaxWidth()
                )

                Spacer(modifier = Modifier.height(8.dp))

                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    OutlinedTextField(
                        value = country,
                        onValueChange = { country = it },
                        label = { Text("Country *") },
                        modifier = Modifier.weight(1f)
                    )

                    OutlinedTextField(
                        value = city,
                        onValueChange = { city = it },
                        label = { Text("City") },
                        modifier = Modifier.weight(1f)
                    )
                }
            } else {
                // Read-only client display
                Text(
                    text = clientData.getDisplayName(),
                    style = MaterialTheme.typography.headlineSmall,
                    fontWeight = FontWeight.Bold
                )

                if (clientData.getContactInfo().isNotBlank()) {
                    Text(
                        text = clientData.getContactInfo(),
                        style = MaterialTheme.typography.bodyMedium
                    )
                }

                if (clientData.getFormattedLocation().isNotBlank()) {
                    Text(
                        text = clientData.getFormattedLocation(),
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        }
    }
}

@Composable
private fun ProductItem(
    product: MiniProduct,
    isEditMode: Boolean,
    onProductUpdate: (MiniProduct) -> Unit,
    onProductRemove: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = product.name,
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )

                if (isEditMode) {
                    TextButton(onClick = onProductRemove) {
                        Text("Remove", color = MaterialTheme.colorScheme.error)
                    }
                }
            }

            Spacer(modifier = Modifier.height(8.dp))

            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text(
                    text = "Qty: ${product.quantity}",
                    style = MaterialTheme.typography.bodyMedium
                )
                Text(
                    text = "${product.quantity * product.unitSellingPrice}",
                    style = MaterialTheme.typography.bodyMedium,
                    fontWeight = FontWeight.Medium
                )
            }

            if (product.description.isNotBlank()) {
                Text(
                    text = product.description,
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
    }
}

@Composable
private fun FinancialSummarySection(
    command: com.tfkcolin.joceladmin.data.models.Command
) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "Financial Summary",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )

            Spacer(modifier = Modifier.height(16.dp))

            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text("Total Selling:")
                Text(
                    text = "${command.currency} ${String.format("%.2f", command.calculateTotalSellingAmount())}",
                    fontWeight = FontWeight.Medium
                )
            }

            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text("Total Buying:")
                Text(
                    text = "${command.currency} ${String.format("%.2f", command.calculateTotalBuyingAmount())}",
                    fontWeight = FontWeight.Medium
                )
            }

            Divider(modifier = Modifier.padding(vertical = 8.dp))

            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text(
                    text = "Profit:",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )
                Text(
                    text = "${command.currency} ${String.format("%.2f", command.getProfitMargin())}",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold,
                    color = if (command.getProfitMargin() >= 0)
                        MaterialTheme.colorScheme.primary
                    else MaterialTheme.colorScheme.error
                )
            }

            if (command.calculateTotalBuyingAmount() > 0) {
                Text(
                    text = "Margin: ${String.format("%.1f", command.getProfitPercentage())}%",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
    }
}

@Composable
private fun ObservationsSection(
    observations: List<String>,
    isEditMode: Boolean,
    onAddObservation: (String) -> Unit
) {
    var newObservation by remember { mutableStateOf("") }

    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "Observations",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )

            Spacer(modifier = Modifier.height(16.dp))

            if (observations.isEmpty()) {
                Text(
                    text = "No observations added",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            } else {
                observations.forEach { observation ->
                    Text(
                        text = "• $observation",
                        style = MaterialTheme.typography.bodyMedium,
                        modifier = Modifier.padding(bottom = 4.dp)
                    )
                }
            }

            if (isEditMode) {
                Spacer(modifier = Modifier.height(16.dp))

                OutlinedTextField(
                    value = newObservation,
                    onValueChange = { newObservation = it },
                    label = { Text("Add observation") },
                    modifier = Modifier.fillMaxWidth(),
                    trailingIcon = {
                        TextButton(
                            onClick = {
                                if (newObservation.isNotBlank()) {
                                    onAddObservation(newObservation)
                                    newObservation = ""
                                }
                            }
                        ) {
                            Text("Add")
                        }
                    }
                )
            }
        }
    }
}

@Composable
private fun PaymentProofSection(
    command: com.tfkcolin.joceladmin.data.models.Command,
    isEditMode: Boolean,
    onUpdatePaymentProof: (String) -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "Payment Proof",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )

            Spacer(modifier = Modifier.height(16.dp))

            if (command.proofUploaded && command.paymentProofImageUrl != null) {
                Text(
                    text = "Payment proof uploaded",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.primary
                )
                // TODO: Display image
            } else {
                Text(
                    text = "No payment proof uploaded",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )

                if (isEditMode) {
                    Spacer(modifier = Modifier.height(8.dp))
                    Button(
                        onClick = {
                            // TODO: Implement image picker
                            onUpdatePaymentProof("placeholder_image_url")
                        }
                    ) {
                        Text("Upload Payment Proof")
                    }
                }
            }
        }
    }
}

@Composable
private fun AddProductDialog(
    onProductAdd: (MiniProduct) -> Unit,
    onDismiss: () -> Unit
) {
    var name by remember { mutableStateOf("") }
    var quantity by remember { mutableStateOf("1") }
    var sellingPrice by remember { mutableStateOf("") }
    var buyingPrice by remember { mutableStateOf("") }
    var description by remember { mutableStateOf("") }

    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("Add Product") },
        text = {
            Column {
                OutlinedTextField(
                    value = name,
                    onValueChange = { name = it },
                    label = { Text("Product Name *") },
                    modifier = Modifier.fillMaxWidth()
                )

                Spacer(modifier = Modifier.height(8.dp))

                OutlinedTextField(
                    value = quantity,
                    onValueChange = { quantity = it },
                    label = { Text("Quantity *") },
                    modifier = Modifier.fillMaxWidth()
                )

                Spacer(modifier = Modifier.height(8.dp))

                OutlinedTextField(
                    value = sellingPrice,
                    onValueChange = { sellingPrice = it },
                    label = { Text("Selling Price *") },
                    modifier = Modifier.fillMaxWidth()
                )

                Spacer(modifier = Modifier.height(8.dp))

                OutlinedTextField(
                    value = buyingPrice,
                    onValueChange = { buyingPrice = it },
                    label = { Text("Buying Price *") },
                    modifier = Modifier.fillMaxWidth()
                )

                Spacer(modifier = Modifier.height(8.dp))

                OutlinedTextField(
                    value = description,
                    onValueChange = { description = it },
                    label = { Text("Description") },
                    modifier = Modifier.fillMaxWidth(),
                    maxLines = 3
                )
            }
        },
        confirmButton = {
            Button(
                onClick = {
                    if (name.isNotBlank() && quantity.isNotBlank() &&
                        sellingPrice.isNotBlank() && buyingPrice.isNotBlank()) {

                        val product = MiniProduct(
                            name = name,
                            quantity = quantity.toIntOrNull() ?: 1,
                            unitSellingPrice = sellingPrice.toDoubleOrNull() ?: 0.0,
                            unitBuyingPrice = buyingPrice.toDoubleOrNull() ?: 0.0,
                            description = description
                        )
                        onProductAdd(product)
                    }
                }
            ) {
                Text("Add")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("Cancel")
            }
        }
    )
}
