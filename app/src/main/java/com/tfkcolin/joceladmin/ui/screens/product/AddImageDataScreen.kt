package com.tfkcolin.joceladmin.ui.screens.product

import android.net.Uri
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.tfkcolin.joceladmin.services.ImageUploadProgress
import com.tfkcolin.joceladmin.services.MultipleImageUploadProgress
import com.tfkcolin.joceladmin.ui.viewmodels.AddImageDataViewModel
import com.tfkcolin.joceladmin.ui.viewmodels.ImageFormData
import com.tfkcolin.joceladmin.utils.ImageUtils

/**
 * Screen for adding new image data
 */
@OptIn(ExperimentalMaterial3Api::class, ExperimentalLayoutApi::class)
@Composable
fun AddImageDataScreen(
    onNavigateBack: () -> Unit,
    viewModel: AddImageDataViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    val context = LocalContext.current
    var currentTag by remember { mutableStateOf("") }
    var showUploadProgress by remember { mutableStateOf(false) }
    var uploadProgressMessage by remember { mutableStateOf("") }

    // Single image picker
    val singleImageLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.GetContent()
    ) { uri: Uri? ->
        uri?.let { viewModel.uploadImage(it) }
    }

    // Multiple image picker
    val multipleImageLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.GetMultipleContents()
    ) { uris: List<Uri> ->
        if (uris.isNotEmpty()) {
            viewModel.uploadMultipleImages(uris)
        }
    }

    // Collect upload progress
    LaunchedEffect(Unit) {
        viewModel.uploadProgress.collect { progress ->
            showUploadProgress = true
            uploadProgressMessage = when (progress) {
                is ImageUploadProgress.Processing -> progress.message
                is ImageUploadProgress.Uploading -> "Uploading ${progress.type}: ${progress.progress}%"
                is ImageUploadProgress.Success -> "Upload completed successfully!"
                is ImageUploadProgress.Error -> "Upload failed: ${progress.message}"
            }
            
            if (progress is ImageUploadProgress.Success || progress is ImageUploadProgress.Error) {
                kotlinx.coroutines.delay(2000)
                showUploadProgress = false
            }
        }
    }

    // Collect multiple upload progress
    LaunchedEffect(Unit) {
        viewModel.multipleUploadProgress.collect { progress ->
            showUploadProgress = true
            uploadProgressMessage = when (progress) {
                is MultipleImageUploadProgress.Started -> "Starting upload of ${progress.totalImages} images..."
                is MultipleImageUploadProgress.ProcessingImage -> "Processing ${progress.filename}..."
                is MultipleImageUploadProgress.ImageCompleted -> "Completed ${progress.completed}/${progress.total} images"
                is MultipleImageUploadProgress.ImageFailed -> "Failed ${progress.filename}: ${progress.error}"
                is MultipleImageUploadProgress.Completed -> {
                    val successCount = progress.results.count { it is com.tfkcolin.joceladmin.services.ImageUploadResult.Success }
                    "Upload completed: $successCount/${progress.results.size} successful"
                }
                else -> uploadProgressMessage
            }
            
            if (progress is MultipleImageUploadProgress.Completed) {
                kotlinx.coroutines.delay(3000)
                showUploadProgress = false
            }
        }
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("Add Image Data") },
                navigationIcon = {
                    IconButton(onClick = onNavigateBack) {
                        Icon(Icons.Default.ArrowBack, contentDescription = "Back")
                    }
                },
                actions = {
                    IconButton(
                        onClick = { viewModel.resetForm() }
                    ) {
                        Icon(Icons.Default.Refresh, contentDescription = "Reset Form")
                    }
                }
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(16.dp)
                .verticalScroll(rememberScrollState()),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // Upload Progress
            if (showUploadProgress) {
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.primaryContainer
                    )
                ) {
                    Column(
                        modifier = Modifier.padding(16.dp)
                    ) {
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            horizontalArrangement = Arrangement.spacedBy(8.dp)
                        ) {
                            if (uiState.isUploading) {
                                CircularProgressIndicator(
                                    modifier = Modifier.size(20.dp),
                                    strokeWidth = 2.dp
                                )
                            } else {
                                Icon(
                                    Icons.Default.CheckCircle,
                                    contentDescription = null,
                                    tint = MaterialTheme.colorScheme.primary
                                )
                            }
                            Text(
                                text = uploadProgressMessage,
                                style = MaterialTheme.typography.bodyMedium
                            )
                        }
                    }
                }
            }

            // Error Display
            uiState.error?.let { error ->
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.errorContainer
                    )
                ) {
                    Row(
                        modifier = Modifier.padding(16.dp),
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        Icon(
                            Icons.Default.Error,
                            contentDescription = null,
                            tint = MaterialTheme.colorScheme.error
                        )
                        Text(
                            text = error,
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onErrorContainer
                        )
                        Spacer(modifier = Modifier.weight(1f))
                        IconButton(onClick = { viewModel.clearError() }) {
                            Icon(Icons.Default.Close, contentDescription = "Dismiss")
                        }
                    }
                }
            }

            // Upload Buttons
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Button(
                    onClick = { singleImageLauncher.launch("image/*") },
                    modifier = Modifier.weight(1f),
                    enabled = !uiState.isUploading
                ) {
                    Icon(Icons.Default.Add, contentDescription = null)
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("Single Image")
                }
                
                Button(
                    onClick = { multipleImageLauncher.launch("image/*") },
                    modifier = Modifier.weight(1f),
                    enabled = !uiState.isUploading
                ) {
                    Icon(Icons.Default.AddPhotoAlternate, contentDescription = null)
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("Multiple Images")
                }
            }

            // Form Fields
            ImageDataForm(
                formData = uiState.formData,
                categories = uiState.categories,
                genres = uiState.genres,
                isLoadingCategories = uiState.isLoadingCategories,
                isLoadingGenres = uiState.isLoadingGenres,
                onFormDataChange = viewModel::updateFormData,
                currentTag = currentTag,
                onCurrentTagChange = { currentTag = it },
                onAddTag = {
                    viewModel.addTag(currentTag)
                    currentTag = ""
                },
                onRemoveTag = viewModel::removeTag
            )
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun ImageDataForm(
    formData: ImageFormData,
    categories: List<com.tfkcolin.joceladmin.data.models.Category>,
    genres: List<com.tfkcolin.joceladmin.data.models.Genre>,
    isLoadingCategories: Boolean,
    isLoadingGenres: Boolean,
    onFormDataChange: (ImageFormData) -> Unit,
    currentTag: String,
    onCurrentTagChange: (String) -> Unit,
    onAddTag: () -> Unit,
    onRemoveTag: (String) -> Unit
) {
    Column(
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // Basic Information
        Card {
            Column(
                modifier = Modifier.padding(16.dp),
                verticalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                Text(
                    text = "Basic Information",
                    style = MaterialTheme.typography.titleMedium
                )

                OutlinedTextField(
                    value = formData.filename,
                    onValueChange = { onFormDataChange(formData.copy(filename = it)) },
                    label = { Text("Filename") },
                    modifier = Modifier.fillMaxWidth(),
                    singleLine = true
                )

                // Category Dropdown
                ExposedDropdownMenuBox(
                    expanded = false,
                    onExpandedChange = { }
                ) {
                    OutlinedTextField(
                        value = categories.find { it.id == formData.category }?.name ?: "",
                        onValueChange = { },
                        readOnly = true,
                        label = { Text("Category") },
                        modifier = Modifier
                            .fillMaxWidth()
                            .menuAnchor(),
                        trailingIcon = {
                            if (isLoadingCategories) {
                                CircularProgressIndicator(modifier = Modifier.size(20.dp))
                            } else {
                                ExposedDropdownMenuDefaults.TrailingIcon(expanded = false)
                            }
                        }
                    )

                    ExposedDropdownMenu(
                        expanded = false,
                        onDismissRequest = { }
                    ) {
                        categories.forEach { category ->
                            DropdownMenuItem(
                                text = { Text(category.name) },
                                onClick = {
                                    onFormDataChange(formData.copy(category = category.id))
                                }
                            )
                        }
                    }
                }

                // Genre Dropdown
                if (formData.category.isNotEmpty()) {
                    ExposedDropdownMenuBox(
                        expanded = false,
                        onExpandedChange = { }
                    ) {
                        OutlinedTextField(
                            value = genres.find { it.id == formData.genre }?.name ?: "",
                            onValueChange = { },
                            readOnly = true,
                            label = { Text("Genre (Optional)") },
                            modifier = Modifier
                                .fillMaxWidth()
                                .menuAnchor(),
                            trailingIcon = {
                                if (isLoadingGenres) {
                                    CircularProgressIndicator(modifier = Modifier.size(20.dp))
                                } else {
                                    ExposedDropdownMenuDefaults.TrailingIcon(expanded = false)
                                }
                            }
                        )

                        ExposedDropdownMenu(
                            expanded = false,
                            onDismissRequest = { }
                        ) {
                            DropdownMenuItem(
                                text = { Text("None") },
                                onClick = {
                                    onFormDataChange(formData.copy(genre = ""))
                                }
                            )
                            genres.forEach { genre ->
                                DropdownMenuItem(
                                    text = { Text(genre.name) },
                                    onClick = {
                                        onFormDataChange(formData.copy(genre = genre.id))
                                    }
                                )
                            }
                        }
                    }
                }

                OutlinedTextField(
                    value = formData.description,
                    onValueChange = { onFormDataChange(formData.copy(description = it)) },
                    label = { Text("Description") },
                    modifier = Modifier.fillMaxWidth(),
                    maxLines = 3
                )

                OutlinedTextField(
                    value = formData.altText,
                    onValueChange = { onFormDataChange(formData.copy(altText = it)) },
                    label = { Text("Alt Text") },
                    modifier = Modifier.fillMaxWidth(),
                    maxLines = 2
                )
            }
        }

        // Tags Section
        TagsSection(
            tags = formData.tags,
            currentTag = currentTag,
            onCurrentTagChange = onCurrentTagChange,
            onAddTag = onAddTag,
            onRemoveTag = onRemoveTag
        )

        // Product Information Section
        ProductInfoSection(
            formData = formData,
            onFormDataChange = onFormDataChange
        )
    }
}

@OptIn(ExperimentalLayoutApi::class)
@Composable
private fun TagsSection(
    tags: List<String>,
    currentTag: String,
    onCurrentTagChange: (String) -> Unit,
    onAddTag: () -> Unit,
    onRemoveTag: (String) -> Unit
) {
    Card {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Text(
                text = "Tags",
                style = MaterialTheme.typography.titleMedium
            )

            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                OutlinedTextField(
                    value = currentTag,
                    onValueChange = onCurrentTagChange,
                    label = { Text("Add Tag") },
                    modifier = Modifier.weight(1f),
                    singleLine = true,
                    keyboardOptions = KeyboardOptions.Default.copy(
                        imeAction = androidx.compose.ui.text.input.ImeAction.Done
                    ),
                    keyboardActions = androidx.compose.foundation.text.KeyboardActions(
                        onDone = { onAddTag() }
                    )
                )

                IconButton(
                    onClick = onAddTag,
                    enabled = currentTag.trim().isNotEmpty()
                ) {
                    Icon(Icons.Default.Add, contentDescription = "Add Tag")
                }
            }

            // Display existing tags
            if (tags.isNotEmpty()) {
                FlowRow(
                    horizontalArrangement = Arrangement.spacedBy(8.dp),
                    verticalArrangement = Arrangement.spacedBy(4.dp)
                ) {
                    tags.forEach { tag ->
                        InputChip(
                            onClick = { },
                            label = { Text(tag) },
                            selected = false,
                            trailingIcon = {
                                Icon(
                                    Icons.Default.Close,
                                    contentDescription = "Remove",
                                    modifier = Modifier
                                        .size(16.dp)
                                        .clickable { onRemoveTag(tag) }
                                )
                            }
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun ProductInfoSection(
    formData: ImageFormData,
    onFormDataChange: (ImageFormData) -> Unit
) {
    Card {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Text(
                    text = "Product Information",
                    style = MaterialTheme.typography.titleMedium,
                    modifier = Modifier.weight(1f)
                )

                Switch(
                    checked = formData.hasProductInfo,
                    onCheckedChange = {
                        onFormDataChange(formData.copy(hasProductInfo = it))
                    }
                )
            }

            if (formData.hasProductInfo) {
                OutlinedTextField(
                    value = formData.productName,
                    onValueChange = { onFormDataChange(formData.copy(productName = it)) },
                    label = { Text("Product Name") },
                    modifier = Modifier.fillMaxWidth(),
                    singleLine = true
                )

                OutlinedTextField(
                    value = formData.productSku,
                    onValueChange = { onFormDataChange(formData.copy(productSku = it)) },
                    label = { Text("Product SKU") },
                    modifier = Modifier.fillMaxWidth(),
                    singleLine = true
                )

                OutlinedTextField(
                    value = if (formData.productPrice == 0.0) "" else formData.productPrice.toString(),
                    onValueChange = {
                        val price = it.toDoubleOrNull() ?: 0.0
                        onFormDataChange(formData.copy(productPrice = price))
                    },
                    label = { Text("Product Price") },
                    modifier = Modifier.fillMaxWidth(),
                    singleLine = true,
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Decimal)
                )

                OutlinedTextField(
                    value = formData.productDescription,
                    onValueChange = { onFormDataChange(formData.copy(productDescription = it)) },
                    label = { Text("Product Description") },
                    modifier = Modifier.fillMaxWidth(),
                    maxLines = 3
                )
            }
        }
    }
}
