package com.tfkcolin.joceladmin.ui.screens.financial

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.FilterList
import androidx.compose.material.icons.filled.Search
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.tfkcolin.joceladmin.data.models.FinancialTransaction
import com.tfkcolin.joceladmin.data.models.TransactionType

/**
 * Financial dashboard screen
 * Displays financial overview, transactions, and country-specific data
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun FinancialDashboardScreen(
    onNavigateToTransactions: () -> Unit,
    onNavigateToCountries: () -> Unit,
    viewModel: FinancialViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    var showFilterDialog by remember { mutableStateOf(false) }
    var searchQuery by remember { mutableStateOf("") }

    LaunchedEffect(searchQuery) {
        viewModel.searchTransactions(searchQuery)
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        // Header with title and actions
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "Financial Dashboard",
                style = MaterialTheme.typography.headlineMedium,
                fontWeight = FontWeight.Bold
            )
            
            Row {
                IconButton(onClick = { showFilterDialog = true }) {
                    Icon(Icons.Default.FilterList, contentDescription = "Filter")
                }
                
                FloatingActionButton(
                    onClick = viewModel::showCreateDialog,
                    modifier = Modifier.size(48.dp)
                ) {
                    Icon(Icons.Default.Add, contentDescription = "Add Transaction")
                }
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        // Search bar
        OutlinedTextField(
            value = searchQuery,
            onValueChange = { searchQuery = it },
            label = { Text("Search transactions") },
            leadingIcon = { Icon(Icons.Default.Search, contentDescription = null) },
            modifier = Modifier.fillMaxWidth(),
            singleLine = true
        )

        Spacer(modifier = Modifier.height(16.dp))

        // Financial overview cards
        if (uiState.statistics.isNotEmpty()) {
            FinancialOverviewSection(
                statistics = uiState.statistics,
                onNavigateToTransactions = onNavigateToTransactions,
                onNavigateToCountries = onNavigateToCountries
            )
            Spacer(modifier = Modifier.height(16.dp))
        }

        // Country summary (if country is selected)
        if (uiState.selectedCountry.isNotBlank() && uiState.countrySummary.isNotEmpty()) {
            CountrySummarySection(
                country = uiState.selectedCountry,
                summary = uiState.countrySummary
            )
            Spacer(modifier = Modifier.height(16.dp))
        }

        // Active filters display
        if (uiState.selectedCountry.isNotBlank() || uiState.selectedType != null) {
            ActiveFiltersRow(
                selectedCountry = uiState.selectedCountry,
                selectedType = uiState.selectedType,
                onClearFilters = viewModel::clearFilters
            )
            Spacer(modifier = Modifier.height(8.dp))
        }

        // Recent transactions
        Text(
            text = "Recent Transactions",
            style = MaterialTheme.typography.titleLarge,
            fontWeight = FontWeight.Bold
        )

        Spacer(modifier = Modifier.height(8.dp))

        when {
            uiState.isLoading -> {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    CircularProgressIndicator()
                }
            }
            
            uiState.errorMessage != null -> {
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.errorContainer
                    )
                ) {
                    Column(
                        modifier = Modifier.padding(16.dp)
                    ) {
                        Text(
                            text = "Error",
                            style = MaterialTheme.typography.titleMedium,
                            color = MaterialTheme.colorScheme.onErrorContainer
                        )
                        Text(
                            text = uiState.errorMessage ?: "Unknown error",
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onErrorContainer
                        )
                        Spacer(modifier = Modifier.height(8.dp))
                        Button(
                            onClick = {
                                viewModel.clearError()
                                viewModel.refresh()
                            }
                        ) {
                            Text("Retry")
                        }
                    }
                }
            }
            
            uiState.transactions.isEmpty() -> {
                Card(
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(32.dp),
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Text(
                            text = "No transactions found",
                            style = MaterialTheme.typography.titleMedium
                        )
                        Text(
                            text = "Create your first transaction to get started",
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                        Spacer(modifier = Modifier.height(16.dp))
                        Button(onClick = viewModel::showCreateDialog) {
                            Text("Create Transaction")
                        }
                    }
                }
            }
            
            else -> {
                LazyColumn(
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    items(uiState.transactions.take(10)) { transaction ->
                        TransactionItem(
                            transaction = transaction,
                            onToggleMark = { marked ->
                                viewModel.toggleTransactionMark(transaction.id, marked)
                            }
                        )
                    }
                    
                    if (uiState.transactions.size > 10) {
                        item {
                            Card(
                                modifier = Modifier.fillMaxWidth()
                            ) {
                                Column(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .padding(16.dp),
                                    horizontalAlignment = Alignment.CenterHorizontally
                                ) {
                                    Text(
                                        text = "${uiState.transactions.size - 10} more transactions",
                                        style = MaterialTheme.typography.bodyMedium,
                                        color = MaterialTheme.colorScheme.onSurfaceVariant
                                    )
                                    Spacer(modifier = Modifier.height(8.dp))
                                    Button(onClick = onNavigateToTransactions) {
                                        Text("View All Transactions")
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    // Filter dialog
    if (showFilterDialog) {
        FinancialFilterDialog(
            countries = uiState.countries,
            selectedCountry = uiState.selectedCountry,
            selectedType = uiState.selectedType,
            onCountrySelected = { country ->
                viewModel.filterByCountry(country)
                showFilterDialog = false
            },
            onTypeSelected = { type ->
                viewModel.filterByType(type)
                showFilterDialog = false
            },
            onDismiss = { showFilterDialog = false }
        )
    }

    // Create transaction dialog
    if (uiState.showCreateDialog) {
        CreateTransactionDialog(
            countries = uiState.countries,
            onTransactionCreate = { transaction ->
                viewModel.createTransaction(transaction)
            },
            onDismiss = viewModel::hideCreateDialog
        )
    }
}

@Composable
private fun FinancialOverviewSection(
    statistics: Map<String, Any>,
    onNavigateToTransactions: () -> Unit,
    onNavigateToCountries: () -> Unit
) {
    Column {
        Text(
            text = "Financial Overview",
            style = MaterialTheme.typography.titleLarge,
            fontWeight = FontWeight.Bold
        )

        Spacer(modifier = Modifier.height(8.dp))

        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            FinancialCard(
                title = "Total Income",
                value = (statistics["totalIncome"] as? Double)?.let {
                    "USD ${String.format("%.2f", it)}"
                } ?: "USD 0.00",
                color = MaterialTheme.colorScheme.primaryContainer,
                modifier = Modifier.weight(1f)
            )

            FinancialCard(
                title = "Total Expenses",
                value = (statistics["totalExpenses"] as? Double)?.let {
                    "USD ${String.format("%.2f", it)}"
                } ?: "USD 0.00",
                color = MaterialTheme.colorScheme.errorContainer,
                modifier = Modifier.weight(1f)
            )
        }

        Spacer(modifier = Modifier.height(8.dp))

        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            FinancialCard(
                title = "Net Profit",
                value = (statistics["netProfit"] as? Double)?.let {
                    "USD ${String.format("%.2f", it)}"
                } ?: "USD 0.00",
                color = MaterialTheme.colorScheme.tertiaryContainer,
                modifier = Modifier.weight(1f)
            )

            FinancialCard(
                title = "Transactions",
                value = (statistics["transactionCount"] as? Int)?.toString() ?: "0",
                color = MaterialTheme.colorScheme.secondaryContainer,
                modifier = Modifier.weight(1f)
            )
        }

        Spacer(modifier = Modifier.height(8.dp))

        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            Button(
                onClick = onNavigateToTransactions,
                modifier = Modifier.weight(1f)
            ) {
                Text("View All Transactions")
            }

            OutlinedButton(
                onClick = onNavigateToCountries,
                modifier = Modifier.weight(1f)
            ) {
                Text("Manage Countries")
            }
        }
    }
}

@Composable
private fun FinancialCard(
    title: String,
    value: String,
    color: androidx.compose.ui.graphics.Color,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier,
        colors = CardDefaults.cardColors(containerColor = color)
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = value,
                style = MaterialTheme.typography.headlineSmall,
                fontWeight = FontWeight.Bold
            )
            Text(
                text = title,
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}

@Composable
private fun CountrySummarySection(
    country: String,
    summary: Map<String, Double>
) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "$country Summary",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )

            Spacer(modifier = Modifier.height(8.dp))

            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Column {
                    Text("Income:")
                    Text(
                        text = "USD ${String.format("%.2f", summary["income"] ?: 0.0)}",
                        fontWeight = FontWeight.Medium,
                        color = MaterialTheme.colorScheme.primary
                    )
                }

                Column {
                    Text("Expenses:")
                    Text(
                        text = "USD ${String.format("%.2f", summary["expenses"] ?: 0.0)}",
                        fontWeight = FontWeight.Medium,
                        color = MaterialTheme.colorScheme.error
                    )
                }

                Column {
                    Text("Profit:")
                    Text(
                        text = "USD ${String.format("%.2f", summary["profit"] ?: 0.0)}",
                        fontWeight = FontWeight.Medium,
                        color = if ((summary["profit"] ?: 0.0) >= 0)
                            MaterialTheme.colorScheme.primary
                        else MaterialTheme.colorScheme.error
                    )
                }
            }
        }
    }
}

@Composable
private fun ActiveFiltersRow(
    selectedCountry: String,
    selectedType: TransactionType?,
    onClearFilters: () -> Unit
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.spacedBy(8.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = "Active filters:",
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )

        if (selectedCountry.isNotBlank()) {
            FilterChip(
                selected = true,
                onClick = onClearFilters,
                label = { Text(selectedCountry) }
            )
        }

        if (selectedType != null) {
            FilterChip(
                selected = true,
                onClick = onClearFilters,
                label = { Text(selectedType.label) }
            )
        }

        TextButton(onClick = onClearFilters) {
            Text("Clear all")
        }
    }
}
