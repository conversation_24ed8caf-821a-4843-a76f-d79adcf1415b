package com.tfkcolin.joceladmin.ui.screens.cargo

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.tfkcolin.joceladmin.data.models.Cargo
import com.tfkcolin.joceladmin.data.models.CargoStatus
import com.tfkcolin.joceladmin.data.models.Shipment
import com.tfkcolin.joceladmin.repository.CargoRepository
import com.tfkcolin.joceladmin.repository.ShipmentRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * ViewModel for cargo details screen
 * Handles individual cargo management, editing, and shipment assignment
 */
@HiltViewModel
class CargoDetailsViewModel @Inject constructor(
    private val cargoRepository: CargoRepository,
    private val shipmentRepository: ShipmentRepository
) : ViewModel() {

    private val _uiState = MutableStateFlow(CargoDetailsUiState())
    val uiState: StateFlow<CargoDetailsUiState> = _uiState.asStateFlow()

    /**
     * Load cargo by ID
     */
    fun loadCargo(cargoId: String) {
        if (cargoId.isBlank()) {
            // Create new cargo mode
            _uiState.value = _uiState.value.copy(
                cargo = Cargo(),
                isEditMode = true,
                isNewCargo = true
            )
            return
        }

        _uiState.value = _uiState.value.copy(isLoading = true, errorMessage = null)

        viewModelScope.launch {
            cargoRepository.getCargoById(cargoId).fold(
                onSuccess = { cargo ->
                    _uiState.value = _uiState.value.copy(
                        cargo = cargo,
                        isLoading = false,
                        errorMessage = null
                    )
                    loadCargoShipments(cargoId)
                },
                onFailure = { exception ->
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        errorMessage = exception.message ?: "Failed to load cargo"
                    )
                }
            )
        }
    }

    /**
     * Load shipments for this cargo
     */
    private fun loadCargoShipments(cargoId: String) {
        viewModelScope.launch {
            shipmentRepository.getShipmentsByCargoId(cargoId).fold(
                onSuccess = { shipments ->
                    _uiState.value = _uiState.value.copy(shipments = shipments)
                },
                onFailure = { /* Ignore shipment loading errors */ }
            )
        }
    }

    /**
     * Save cargo (create or update)
     */
    fun saveCargo() {
        val currentCargo = _uiState.value.cargo
        if (!currentCargo.isValid()) {
            _uiState.value = _uiState.value.copy(
                errorMessage = "Please fill in all required fields"
            )
            return
        }

        _uiState.value = _uiState.value.copy(isSaving = true, errorMessage = null)

        viewModelScope.launch {
            val result = if (_uiState.value.isNewCargo) {
                cargoRepository.createCargo(currentCargo)
            } else {
                cargoRepository.updateCargo(currentCargo)
            }

            result.fold(
                onSuccess = { savedCargo ->
                    _uiState.value = _uiState.value.copy(
                        cargo = savedCargo,
                        isSaving = false,
                        isEditMode = false,
                        isNewCargo = false,
                        saveSuccessful = true
                    )
                },
                onFailure = { exception ->
                    _uiState.value = _uiState.value.copy(
                        isSaving = false,
                        errorMessage = exception.message ?: "Failed to save cargo"
                    )
                }
            )
        }
    }

    /**
     * Update cargo basic information
     */
    fun updateCargoInfo(
        origin: String,
        destination: String,
        carrier: String,
        vesselName: String,
        route: String
    ) {
        val updatedCargo = _uiState.value.cargo.copy(
            origin = origin,
            destination = destination,
            carrier = carrier,
            vesselName = vesselName,
            route = route
        )
        _uiState.value = _uiState.value.copy(cargo = updatedCargo)
    }

    /**
     * Update cargo capacity
     */
    fun updateCargoCapacity(maxWeight: Double, maxVolume: Double) {
        val updatedCargo = _uiState.value.cargo.copy(
            maxWeightKg = maxWeight,
            maxVolumeCbm = maxVolume
        )
        _uiState.value = _uiState.value.copy(cargo = updatedCargo)
    }

    /**
     * Progress cargo to next status
     */
    fun progressStatus() {
        val currentStatus = _uiState.value.cargo.getStatus()
        val nextStatus = currentStatus.getNext()
        
        if (nextStatus != null) {
            updateCargoStatus(nextStatus)
        }
    }

    /**
     * Regress cargo to previous status
     */
    fun regressStatus() {
        val currentStatus = _uiState.value.cargo.getStatus()
        val previousStatus = currentStatus.getPrevious()
        
        if (previousStatus != null) {
            updateCargoStatus(previousStatus)
        }
    }

    /**
     * Update cargo status
     */
    private fun updateCargoStatus(status: CargoStatus) {
        viewModelScope.launch {
            cargoRepository.updateCargoStatus(_uiState.value.cargo.id, status).fold(
                onSuccess = { updatedCargo ->
                    _uiState.value = _uiState.value.copy(cargo = updatedCargo)
                },
                onFailure = { exception ->
                    _uiState.value = _uiState.value.copy(
                        errorMessage = "Failed to update status: ${exception.message}"
                    )
                }
            )
        }
    }

    /**
     * Assign shipment to cargo
     */
    fun assignShipment(shipmentId: String) {
        viewModelScope.launch {
            cargoRepository.addShipmentToCargo(_uiState.value.cargo.id, shipmentId).fold(
                onSuccess = { updatedCargo ->
                    _uiState.value = _uiState.value.copy(cargo = updatedCargo)
                    loadCargoShipments(_uiState.value.cargo.id)
                },
                onFailure = { exception ->
                    _uiState.value = _uiState.value.copy(
                        errorMessage = "Failed to assign shipment: ${exception.message}"
                    )
                }
            )
        }
    }

    /**
     * Remove shipment from cargo
     */
    fun removeShipment(shipmentId: String) {
        viewModelScope.launch {
            cargoRepository.removeShipmentFromCargo(_uiState.value.cargo.id, shipmentId).fold(
                onSuccess = { updatedCargo ->
                    _uiState.value = _uiState.value.copy(cargo = updatedCargo)
                    loadCargoShipments(_uiState.value.cargo.id)
                },
                onFailure = { exception ->
                    _uiState.value = _uiState.value.copy(
                        errorMessage = "Failed to remove shipment: ${exception.message}"
                    )
                }
            )
        }
    }

    /**
     * Load pending shipments for assignment
     */
    fun loadPendingShipments() {
        viewModelScope.launch {
            shipmentRepository.getPendingShipments().fold(
                onSuccess = { shipments ->
                    _uiState.value = _uiState.value.copy(pendingShipments = shipments)
                },
                onFailure = { /* Ignore errors */ }
            )
        }
    }

    /**
     * Toggle edit mode
     */
    fun toggleEditMode() {
        _uiState.value = _uiState.value.copy(
            isEditMode = !_uiState.value.isEditMode,
            errorMessage = null
        )
    }

    /**
     * Clear error message
     */
    fun clearError() {
        _uiState.value = _uiState.value.copy(errorMessage = null)
    }

    /**
     * Clear save success flag
     */
    fun clearSaveSuccess() {
        _uiState.value = _uiState.value.copy(saveSuccessful = false)
    }
}

/**
 * UI state for cargo details screen
 */
data class CargoDetailsUiState(
    val cargo: Cargo = Cargo(),
    val shipments: List<Shipment> = emptyList(),
    val pendingShipments: List<Shipment> = emptyList(),
    val isLoading: Boolean = false,
    val isSaving: Boolean = false,
    val isEditMode: Boolean = false,
    val isNewCargo: Boolean = false,
    val saveSuccessful: Boolean = false,
    val errorMessage: String? = null
)
