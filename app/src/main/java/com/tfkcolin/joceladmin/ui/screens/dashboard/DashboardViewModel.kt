package com.tfkcolin.joceladmin.ui.screens.dashboard

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.tfkcolin.joceladmin.repository.AuthRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.AttachMoney
import androidx.compose.material.icons.filled.List
import androidx.compose.material.icons.filled.LocalShipping
import androidx.compose.material.icons.filled.Person
import androidx.compose.material.icons.filled.PersonAdd
import androidx.compose.material.icons.filled.Photo
import androidx.compose.material.icons.filled.Schedule
import androidx.compose.material.icons.filled.TrendingUp
import javax.inject.Inject

/**
 * ViewModel for dashboard screen
 * Manages dashboard data and user information
 */
@HiltViewModel
class DashboardViewModel @Inject constructor(
    private val authRepository: AuthRepository
) : ViewModel() {

    private val _uiState = MutableStateFlow(DashboardUiState())
    val uiState: StateFlow<DashboardUiState> = _uiState.asStateFlow()

    init {
        loadDashboardData()
    }

    /**
     * Load dashboard data
     */
    private fun loadDashboardData() {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true)
            
            try {
                // Get current user info
                authRepository.getCurrentUser().collect { firebaseUser ->
                    if (firebaseUser != null) {
                        val user = authRepository.getUserData(firebaseUser.uid)
                        _uiState.value = _uiState.value.copy(
                            userName = user.displayName.ifEmpty { "User" },
                            userRole = user.role.displayName,
                            isLoading = false
                        )
                    }
                }
                
                // Load quick stats (mock data for now)
                loadQuickStats()
                
                // Load recent activities (mock data for now)
                loadRecentActivities()
                
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    errorMessage = e.message ?: "Failed to load dashboard data"
                )
            }
        }
    }

    /**
     * Load quick statistics
     */
    private fun loadQuickStats() {
        // TODO: Replace with actual data from repositories
        val stats = listOf(
            QuickStat(
                title = "Active Commands",
                value = "24",
                icon = Icons.Default.List
            ),
            QuickStat(
                title = "In Transit",
                value = "8",
                icon = Icons.Default.LocalShipping
            ),
            QuickStat(
                title = "This Month",
                value = "$45.2K",
                icon = Icons.Default.TrendingUp
            ),
            QuickStat(
                title = "Pending",
                value = "12",
                icon = Icons.Default.Schedule
            )
        )
        
        _uiState.value = _uiState.value.copy(quickStats = stats)
    }

    /**
     * Load recent activities
     */
    private fun loadRecentActivities() {
        // TODO: Replace with actual data from repositories
        val activities = listOf(
            RecentActivity(
                title = "New command created",
                description = "Command #CMD-2024-001 for client ABC Corp",
                type = "command",
                timeAgo = "2 hours ago",
                icon = Icons.Default.Add
            ),
            RecentActivity(
                title = "Cargo shipped",
                description = "Cargo #CRG-2024-005 departed from Shanghai",
                type = "cargo",
                timeAgo = "4 hours ago",
                icon = Icons.Default.LocalShipping
            ),
            RecentActivity(
                title = "Payment received",
                description = "Payment of $12,500 for command #CMD-2024-002",
                type = "financial",
                timeAgo = "6 hours ago",
                icon = Icons.Default.AttachMoney
            ),
            RecentActivity(
                title = "User registered",
                description = "New employee John Doe joined the system",
                type = "user",
                timeAgo = "1 day ago",
                icon = Icons.Default.PersonAdd
            ),
            RecentActivity(
                title = "Images uploaded",
                description = "25 new product images added to catalog",
                type = "product",
                timeAgo = "2 days ago",
                icon = Icons.Default.Photo
            )
        )
        
        _uiState.value = _uiState.value.copy(recentActivities = activities)
    }

    /**
     * Refresh dashboard data
     */
    fun refresh() {
        loadDashboardData()
    }

    /**
     * Clear error message
     */
    fun clearError() {
        _uiState.value = _uiState.value.copy(errorMessage = null)
    }
}

/**
 * UI state for dashboard screen
 */
data class DashboardUiState(
    val userName: String = "",
    val userRole: String = "",
    val isLoading: Boolean = false,
    val errorMessage: String? = null,
    val quickStats: List<QuickStat> = emptyList(),
    val recentActivities: List<RecentActivity> = emptyList()
)
