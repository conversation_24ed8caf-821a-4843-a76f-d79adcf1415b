package com.tfkcolin.joceladmin.ui.screens.cargo

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.tfkcolin.joceladmin.data.models.Cargo
import com.tfkcolin.joceladmin.data.models.CargoStatus
import com.tfkcolin.joceladmin.repository.CargoRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * ViewModel for cargo list screen
 * Handles cargo list management, filtering, and search
 */
@HiltViewModel
class CargoListViewModel @Inject constructor(
    private val cargoRepository: CargoRepository
) : ViewModel() {

    private val _uiState = MutableStateFlow(CargoListUiState())
    val uiState: StateFlow<CargoListUiState> = _uiState.asStateFlow()

    init {
        loadCargos()
        loadStatistics()
    }

    /**
     * Load cargos based on current filters
     */
    fun loadCargos() {
        _uiState.value = _uiState.value.copy(isLoading = true, errorMessage = null)

        viewModelScope.launch {
            val result = when {
                _uiState.value.selectedStatus != null -> {
                    cargoRepository.getCargosByStatus(_uiState.value.selectedStatus!!)
                }
                _uiState.value.selectedOrigin.isNotBlank() -> {
                    cargoRepository.getCargosByOrigin(_uiState.value.selectedOrigin)
                }
                _uiState.value.selectedDestination.isNotBlank() -> {
                    cargoRepository.getCargosByDestination(_uiState.value.selectedDestination)
                }
                _uiState.value.searchQuery.isNotBlank() -> {
                    cargoRepository.searchCargosByRoute(_uiState.value.searchQuery)
                }
                else -> {
                    cargoRepository.getRecentCargos(50)
                }
            }

            result.fold(
                onSuccess = { cargos ->
                    _uiState.value = _uiState.value.copy(
                        cargos = cargos,
                        isLoading = false,
                        errorMessage = null
                    )
                },
                onFailure = { exception ->
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        errorMessage = exception.message ?: "Failed to load cargos"
                    )
                }
            )
        }
    }

    /**
     * Load cargo statistics
     */
    private fun loadStatistics() {
        viewModelScope.launch {
            cargoRepository.getCargoStatistics().fold(
                onSuccess = { stats ->
                    _uiState.value = _uiState.value.copy(statistics = stats)
                },
                onFailure = { /* Ignore statistics errors */ }
            )
        }
    }

    /**
     * Filter cargos by status
     */
    fun filterByStatus(status: CargoStatus?) {
        _uiState.value = _uiState.value.copy(
            selectedStatus = status,
            selectedOrigin = "",
            selectedDestination = "",
            searchQuery = ""
        )
        loadCargos()
    }

    /**
     * Filter cargos by origin
     */
    fun filterByOrigin(origin: String) {
        _uiState.value = _uiState.value.copy(
            selectedOrigin = origin,
            selectedStatus = null,
            selectedDestination = "",
            searchQuery = ""
        )
        loadCargos()
    }

    /**
     * Filter cargos by destination
     */
    fun filterByDestination(destination: String) {
        _uiState.value = _uiState.value.copy(
            selectedDestination = destination,
            selectedStatus = null,
            selectedOrigin = "",
            searchQuery = ""
        )
        loadCargos()
    }

    /**
     * Search cargos by route
     */
    fun searchCargos(query: String) {
        _uiState.value = _uiState.value.copy(
            searchQuery = query,
            selectedStatus = null,
            selectedOrigin = "",
            selectedDestination = ""
        )
        if (query.length >= 2) { // Only search with 2+ characters
            loadCargos()
        } else if (query.isEmpty()) {
            loadCargos() // Load all when search is cleared
        }
    }

    /**
     * Clear all filters
     */
    fun clearFilters() {
        _uiState.value = _uiState.value.copy(
            selectedStatus = null,
            selectedOrigin = "",
            selectedDestination = "",
            searchQuery = ""
        )
        loadCargos()
    }

    /**
     * Refresh cargos
     */
    fun refresh() {
        loadCargos()
        loadStatistics()
    }

    /**
     * Update cargo status
     */
    fun updateCargoStatus(cargoId: String, status: CargoStatus) {
        viewModelScope.launch {
            cargoRepository.updateCargoStatus(cargoId, status).fold(
                onSuccess = {
                    loadCargos() // Refresh list
                },
                onFailure = { exception ->
                    _uiState.value = _uiState.value.copy(
                        errorMessage = "Failed to update status: ${exception.message}"
                    )
                }
            )
        }
    }

    /**
     * Get available cargos for shipment assignment
     */
    fun loadAvailableCargos() {
        viewModelScope.launch {
            cargoRepository.getAvailableCargos().fold(
                onSuccess = { cargos ->
                    _uiState.value = _uiState.value.copy(
                        availableCargos = cargos
                    )
                },
                onFailure = { /* Ignore errors for this optional feature */ }
            )
        }
    }

    /**
     * Clear error message
     */
    fun clearError() {
        _uiState.value = _uiState.value.copy(errorMessage = null)
    }
}

/**
 * UI state for cargo list screen
 */
data class CargoListUiState(
    val cargos: List<Cargo> = emptyList(),
    val availableCargos: List<Cargo> = emptyList(),
    val isLoading: Boolean = false,
    val errorMessage: String? = null,
    val selectedStatus: CargoStatus? = null,
    val selectedOrigin: String = "",
    val selectedDestination: String = "",
    val searchQuery: String = "",
    val statistics: Map<String, Int> = emptyMap()
)
