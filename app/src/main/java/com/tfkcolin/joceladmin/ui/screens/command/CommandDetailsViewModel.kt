package com.tfkcolin.joceladmin.ui.screens.command

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.tfkcolin.joceladmin.data.models.Command
import com.tfkcolin.joceladmin.data.models.CommandStatus
import com.tfkcolin.joceladmin.data.models.ClientData
import com.tfkcolin.joceladmin.data.models.MiniProduct
import com.tfkcolin.joceladmin.repository.CommandRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * ViewModel for command details screen
 * Handles individual command management, editing, and workflow progression
 */
@HiltViewModel
class CommandDetailsViewModel @Inject constructor(
    private val commandRepository: CommandRepository
) : ViewModel() {

    private val _uiState = MutableStateFlow(CommandDetailsUiState())
    val uiState: StateFlow<CommandDetailsUiState> = _uiState.asStateFlow()

    /**
     * Load command by ID
     */
    fun loadCommand(commandId: String) {
        if (commandId.isBlank()) {
            // Create new command mode
            _uiState.value = _uiState.value.copy(
                command = Command(),
                isEditMode = true,
                isNewCommand = true
            )
            return
        }

        _uiState.value = _uiState.value.copy(isLoading = true, errorMessage = null)

        viewModelScope.launch {
            commandRepository.getCommandById(commandId).fold(
                onSuccess = { command ->
                    _uiState.value = _uiState.value.copy(
                        command = command,
                        isLoading = false,
                        errorMessage = null
                    )
                },
                onFailure = { exception ->
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        errorMessage = exception.message ?: "Failed to load command"
                    )
                }
            )
        }
    }

    /**
     * Save command (create or update)
     */
    fun saveCommand() {
        val currentCommand = _uiState.value.command
        if (!currentCommand.isValid()) {
            _uiState.value = _uiState.value.copy(
                errorMessage = "Please fill in all required fields"
            )
            return
        }

        _uiState.value = _uiState.value.copy(isSaving = true, errorMessage = null)

        viewModelScope.launch {
            val result = if (_uiState.value.isNewCommand) {
                commandRepository.createCommand(currentCommand)
            } else {
                commandRepository.updateCommand(currentCommand)
            }

            result.fold(
                onSuccess = { savedCommand ->
                    _uiState.value = _uiState.value.copy(
                        command = savedCommand,
                        isSaving = false,
                        isEditMode = false,
                        isNewCommand = false,
                        saveSuccessful = true
                    )
                },
                onFailure = { exception ->
                    _uiState.value = _uiState.value.copy(
                        isSaving = false,
                        errorMessage = exception.message ?: "Failed to save command"
                    )
                }
            )
        }
    }

    /**
     * Update client data
     */
    fun updateClientData(clientData: ClientData) {
        val updatedCommand = _uiState.value.command.copy(client = clientData)
        _uiState.value = _uiState.value.copy(command = updatedCommand)
    }

    /**
     * Add product to command
     */
    fun addProduct(product: MiniProduct) {
        val updatedProducts = _uiState.value.command.products + product
        val updatedCommand = _uiState.value.command.copy(products = updatedProducts)
        _uiState.value = _uiState.value.copy(command = updatedCommand)
    }

    /**
     * Update product in command
     */
    fun updateProduct(productIndex: Int, product: MiniProduct) {
        val updatedProducts = _uiState.value.command.products.toMutableList()
        if (productIndex in updatedProducts.indices) {
            updatedProducts[productIndex] = product
            val updatedCommand = _uiState.value.command.copy(products = updatedProducts)
            _uiState.value = _uiState.value.copy(command = updatedCommand)
        }
    }

    /**
     * Remove product from command
     */
    fun removeProduct(productIndex: Int) {
        val updatedProducts = _uiState.value.command.products.toMutableList()
        if (productIndex in updatedProducts.indices) {
            updatedProducts.removeAt(productIndex)
            val updatedCommand = _uiState.value.command.copy(products = updatedProducts)
            _uiState.value = _uiState.value.copy(command = updatedCommand)
        }
    }

    /**
     * Progress command to next status
     */
    fun progressStatus() {
        val currentStatus = _uiState.value.command.getStatus()
        val nextStatus = CommandStatus.values().getOrNull(currentStatus.ordinal + 1)
        
        if (nextStatus != null) {
            updateCommandStatus(nextStatus)
        }
    }

    /**
     * Regress command to previous status
     */
    fun regressStatus() {
        val currentStatus = _uiState.value.command.getStatus()
        val previousStatus = CommandStatus.values().getOrNull(currentStatus.ordinal - 1)
        
        if (previousStatus != null) {
            updateCommandStatus(previousStatus)
        }
    }

    /**
     * Update command status
     */
    private fun updateCommandStatus(status: CommandStatus) {
        viewModelScope.launch {
            commandRepository.updateCommandStatus(_uiState.value.command.id, status).fold(
                onSuccess = { updatedCommand ->
                    _uiState.value = _uiState.value.copy(command = updatedCommand)
                },
                onFailure = { exception ->
                    _uiState.value = _uiState.value.copy(
                        errorMessage = "Failed to update status: ${exception.message}"
                    )
                }
            )
        }
    }

    /**
     * Add observation note
     */
    fun addObservation(note: String) {
        if (note.isBlank()) return

        viewModelScope.launch {
            commandRepository.addObservation(_uiState.value.command.id, note).fold(
                onSuccess = { updatedCommand ->
                    _uiState.value = _uiState.value.copy(command = updatedCommand)
                },
                onFailure = { exception ->
                    _uiState.value = _uiState.value.copy(
                        errorMessage = "Failed to add observation: ${exception.message}"
                    )
                }
            )
        }
    }

    /**
     * Update payment proof
     */
    fun updatePaymentProof(imageUrl: String) {
        viewModelScope.launch {
            commandRepository.updatePaymentProof(_uiState.value.command.id, imageUrl).fold(
                onSuccess = { updatedCommand ->
                    _uiState.value = _uiState.value.copy(command = updatedCommand)
                },
                onFailure = { exception ->
                    _uiState.value = _uiState.value.copy(
                        errorMessage = "Failed to update payment proof: ${exception.message}"
                    )
                }
            )
        }
    }

    /**
     * Toggle edit mode
     */
    fun toggleEditMode() {
        _uiState.value = _uiState.value.copy(
            isEditMode = !_uiState.value.isEditMode,
            errorMessage = null
        )
    }

    /**
     * Clear error message
     */
    fun clearError() {
        _uiState.value = _uiState.value.copy(errorMessage = null)
    }

    /**
     * Clear save success flag
     */
    fun clearSaveSuccess() {
        _uiState.value = _uiState.value.copy(saveSuccessful = false)
    }

    /**
     * Update command country
     */
    fun updateCountry(country: String) {
        val updatedCommand = _uiState.value.command.copy(country = country)
        _uiState.value = _uiState.value.copy(command = updatedCommand)
    }

    /**
     * Update command currency
     */
    fun updateCurrency(currency: String) {
        val updatedCommand = _uiState.value.command.copy(currency = currency)
        _uiState.value = _uiState.value.copy(command = updatedCommand)
    }
}

/**
 * UI state for command details screen
 */
data class CommandDetailsUiState(
    val command: Command = Command(),
    val isLoading: Boolean = false,
    val isSaving: Boolean = false,
    val isEditMode: Boolean = false,
    val isNewCommand: Boolean = false,
    val saveSuccessful: Boolean = false,
    val errorMessage: String? = null
)
