package com.tfkcolin.joceladmin.ui.viewmodels

import android.net.Uri
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.tfkcolin.joceladmin.data.models.*
import com.tfkcolin.joceladmin.repository.CategoryRepository
import com.tfkcolin.joceladmin.services.ImageUploadProgress
import com.tfkcolin.joceladmin.services.ImageUploadService
import com.tfkcolin.joceladmin.services.MultipleImageUploadProgress
import com.tfkcolin.joceladmin.utils.ImageUtils
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * ViewModel for adding new image data
 */
@HiltViewModel
class AddImageDataViewModel @Inject constructor(
    private val imageUploadService: ImageUploadService,
    private val categoryRepository: CategoryRepository
) : ViewModel() {

    private val _uiState = MutableStateFlow(AddImageDataUiState())
    val uiState: StateFlow<AddImageDataUiState> = _uiState.asStateFlow()

    private val _uploadProgress = MutableSharedFlow<ImageUploadProgress>()
    val uploadProgress: SharedFlow<ImageUploadProgress> = _uploadProgress.asSharedFlow()

    private val _multipleUploadProgress = MutableSharedFlow<MultipleImageUploadProgress>()
    val multipleUploadProgress: SharedFlow<MultipleImageUploadProgress> = _multipleUploadProgress.asSharedFlow()

    init {
        loadCategories()
    }

    /**
     * Load available categories
     */
    private fun loadCategories() {
        viewModelScope.launch {
            _uiState.update { it.copy(isLoadingCategories = true) }
            
            categoryRepository.getCategories().fold(
                onSuccess = { categories ->
                    _uiState.update { 
                        it.copy(
                            categories = categories,
                            isLoadingCategories = false
                        )
                    }
                },
                onFailure = { error ->
                    _uiState.update { 
                        it.copy(
                            isLoadingCategories = false,
                            error = error.message
                        )
                    }
                }
            )
        }
    }

    /**
     * Load genres for selected category
     */
    fun loadGenres(categoryId: String) {
        viewModelScope.launch {
            _uiState.update { it.copy(isLoadingGenres = true) }
            
            categoryRepository.getGenresByCategory(categoryId).fold(
                onSuccess = { genres ->
                    _uiState.update { 
                        it.copy(
                            genres = genres,
                            isLoadingGenres = false
                        )
                    }
                },
                onFailure = { error ->
                    _uiState.update { 
                        it.copy(
                            isLoadingGenres = false,
                            error = error.message
                        )
                    }
                }
            )
        }
    }

    /**
     * Update form data
     */
    fun updateFormData(formData: ImageFormData) {
        _uiState.update { it.copy(formData = formData) }
        
        // Load genres when category changes
        if (formData.category.isNotEmpty() && formData.category != _uiState.value.formData.category) {
            loadGenres(formData.category)
        }
    }

    /**
     * Add tag to the list
     */
    fun addTag(tag: String) {
        val trimmedTag = tag.trim()
        if (trimmedTag.isNotEmpty() && !_uiState.value.formData.tags.contains(trimmedTag)) {
            val updatedTags = _uiState.value.formData.tags + trimmedTag
            updateFormData(_uiState.value.formData.copy(tags = updatedTags))
        }
    }

    /**
     * Remove tag from the list
     */
    fun removeTag(tag: String) {
        val updatedTags = _uiState.value.formData.tags - tag
        updateFormData(_uiState.value.formData.copy(tags = updatedTags))
    }

    /**
     * Upload single image
     */
    fun uploadImage(uri: Uri) {
        val formData = _uiState.value.formData
        
        if (!validateForm(formData)) {
            return
        }

        val filename = ImageUtils.generateUniqueFilename(formData.filename)
        val request = ImageUploadRequest(
            filename = filename,
            category = formData.category,
            genre = formData.genre,
            tags = formData.tags,
            description = formData.description,
            altText = formData.altText,
            productInfo = if (formData.hasProductInfo) {
                ProductInfo(
                    name = formData.productName,
                    sku = formData.productSku,
                    price = formData.productPrice
                )
            } else null
        )

        viewModelScope.launch {
            _uiState.update { it.copy(isUploading = true, error = null) }
            
            imageUploadService.uploadImage(uri, request).collect { progress ->
                _uploadProgress.emit(progress)
                
                when (progress) {
                    is ImageUploadProgress.Success -> {
                        _uiState.update { 
                            it.copy(
                                isUploading = false,
                                uploadedImage = progress.imageData,
                                formData = ImageFormData() // Reset form
                            )
                        }
                    }
                    is ImageUploadProgress.Error -> {
                        _uiState.update { 
                            it.copy(
                                isUploading = false,
                                error = progress.message
                            )
                        }
                    }
                    else -> {
                        // Progress updates handled by SharedFlow
                    }
                }
            }
        }
    }

    /**
     * Upload multiple images
     */
    fun uploadMultipleImages(imageUris: List<Uri>) {
        val formData = _uiState.value.formData
        
        if (!validateForm(formData, checkFilename = false)) {
            return
        }

        val requests = imageUris.mapIndexed { index, uri ->
            val filename = ImageUtils.generateUniqueFilename("image_${index + 1}.jpg")
            val request = ImageUploadRequest(
                filename = filename,
                category = formData.category,
                genre = formData.genre,
                tags = formData.tags,
                description = formData.description,
                altText = formData.altText,
                productInfo = if (formData.hasProductInfo) {
                    ProductInfo(
                        name = formData.productName,
                        sku = formData.productSku,
                        price = formData.productPrice
                    )
                } else null
            )
            uri to request
        }

        viewModelScope.launch {
            _uiState.update { it.copy(isUploading = true, error = null) }
            
            imageUploadService.uploadMultipleImages(requests).collect { progress ->
                _multipleUploadProgress.emit(progress)
                
                when (progress) {
                    is MultipleImageUploadProgress.Completed -> {
                        val successCount = progress.results.count { it is com.tfkcolin.joceladmin.services.ImageUploadResult.Success }
                        _uiState.update { 
                            it.copy(
                                isUploading = false,
                                uploadResults = progress.results,
                                formData = ImageFormData() // Reset form
                            )
                        }
                    }
                    else -> {
                        // Progress updates handled by SharedFlow
                    }
                }
            }
        }
    }

    /**
     * Validate form data
     */
    private fun validateForm(formData: ImageFormData, checkFilename: Boolean = true): Boolean {
        val errors = mutableListOf<String>()
        
        if (checkFilename && formData.filename.isBlank()) {
            errors.add("Filename is required")
        }
        
        if (formData.category.isBlank()) {
            errors.add("Category is required")
        }
        
        if (checkFilename && !ImageUtils.isValidImageFile(formData.filename)) {
            errors.add("Invalid image file format")
        }
        
        if (formData.hasProductInfo) {
            if (formData.productName.isBlank()) {
                errors.add("Product name is required")
            }
            if (formData.productPrice < 0) {
                errors.add("Product price must be positive")
            }
        }
        
        if (errors.isNotEmpty()) {
            _uiState.update { it.copy(error = errors.joinToString(", ")) }
            return false
        }
        
        return true
    }

    /**
     * Clear error message
     */
    fun clearError() {
        _uiState.update { it.copy(error = null) }
    }

    /**
     * Reset form
     */
    fun resetForm() {
        _uiState.update { 
            it.copy(
                formData = ImageFormData(),
                uploadedImage = null,
                uploadResults = emptyList(),
                error = null
            )
        }
    }
}

/**
 * UI State for Add Image Data screen
 */
data class AddImageDataUiState(
    val formData: ImageFormData = ImageFormData(),
    val categories: List<Category> = emptyList(),
    val genres: List<Genre> = emptyList(),
    val isLoadingCategories: Boolean = false,
    val isLoadingGenres: Boolean = false,
    val isUploading: Boolean = false,
    val uploadedImage: ImageData? = null,
    val uploadResults: List<com.tfkcolin.joceladmin.services.ImageUploadResult> = emptyList(),
    val error: String? = null
)

/**
 * Form data for image upload
 */
data class ImageFormData(
    val filename: String = "",
    val category: String = "",
    val genre: String = "",
    val tags: List<String> = emptyList(),
    val description: String = "",
    val altText: String = "",
    val hasProductInfo: Boolean = false,
    val productName: String = "",
    val productSku: String = "",
    val productPrice: Double = 0.0,
    val productDescription: String = ""
)
