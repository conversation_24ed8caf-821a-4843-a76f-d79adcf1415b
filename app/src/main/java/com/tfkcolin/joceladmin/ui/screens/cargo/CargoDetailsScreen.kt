package com.tfkcolin.joceladmin.ui.screens.cargo

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Edit
import androidx.compose.material.icons.filled.Save
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.tfkcolin.joceladmin.data.models.Shipment

/**
 * Cargo details screen
 * Displays and allows editing of individual cargo container details
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CargoDetailsScreen(
    cargoId: String,
    onNavigateBack: () -> Unit,
    viewModel: CargoDetailsViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()

    LaunchedEffect(cargoId) {
        viewModel.loadCargo(cargoId)
    }

    LaunchedEffect(uiState.saveSuccessful) {
        if (uiState.saveSuccessful) {
            viewModel.clearSaveSuccess()
            if (uiState.isNewCargo) {
                onNavigateBack()
            }
        }
    }

    Column(
        modifier = Modifier.fillMaxSize()
    ) {
        // Top app bar
        TopAppBar(
            title = { 
                Text(
                    if (uiState.isNewCargo) "New Cargo Container" 
                    else uiState.cargo.getFormattedContainerNumber()
                )
            },
            navigationIcon = {
                IconButton(onClick = onNavigateBack) {
                    Icon(Icons.Default.ArrowBack, contentDescription = "Back")
                }
            },
            actions = {
                if (uiState.isEditMode) {
                    IconButton(
                        onClick = viewModel::saveCargo,
                        enabled = !uiState.isSaving
                    ) {
                        if (uiState.isSaving) {
                            CircularProgressIndicator(
                                modifier = Modifier.size(24.dp),
                                strokeWidth = 2.dp
                            )
                        } else {
                            Icon(Icons.Default.Save, contentDescription = "Save")
                        }
                    }
                } else {
                    IconButton(onClick = viewModel::toggleEditMode) {
                        Icon(Icons.Default.Edit, contentDescription = "Edit")
                    }
                }
            }
        )

        when {
            uiState.isLoading -> {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    CircularProgressIndicator()
                }
            }
            
            uiState.errorMessage != null -> {
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.errorContainer
                    )
                ) {
                    Column(
                        modifier = Modifier.padding(16.dp)
                    ) {
                        Text(
                            text = "Error",
                            style = MaterialTheme.typography.titleMedium,
                            color = MaterialTheme.colorScheme.onErrorContainer
                        )
                        Text(
                            text = uiState.errorMessage ?: "Unknown error",
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onErrorContainer
                        )
                        Spacer(modifier = Modifier.height(8.dp))
                        Button(onClick = viewModel::clearError) {
                            Text("Dismiss")
                        }
                    }
                }
            }
            
            else -> {
                LazyColumn(
                    modifier = Modifier.fillMaxSize(),
                    contentPadding = PaddingValues(16.dp),
                    verticalArrangement = Arrangement.spacedBy(16.dp)
                ) {
                    // Cargo status section
                    item {
                        CargoStatusSection(
                            cargo = uiState.cargo,
                            isEditMode = uiState.isEditMode,
                            onProgressStatus = viewModel::progressStatus,
                            onRegressStatus = viewModel::regressStatus
                        )
                    }

                    // Cargo information section
                    item {
                        CargoInfoSection(
                            cargo = uiState.cargo,
                            isEditMode = uiState.isEditMode,
                            onCargoInfoUpdate = viewModel::updateCargoInfo
                        )
                    }

                    // Capacity section
                    item {
                        CargoCapacitySection(
                            cargo = uiState.cargo,
                            isEditMode = uiState.isEditMode,
                            onCapacityUpdate = viewModel::updateCargoCapacity
                        )
                    }

                    // Shipments section header
                    item {
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceBetween,
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "Shipments (${uiState.shipments.size})",
                                style = MaterialTheme.typography.titleLarge,
                                fontWeight = FontWeight.Bold
                            )
                            
                            if (uiState.isEditMode && uiState.cargo.canAcceptShipments()) {
                                Button(
                                    onClick = { viewModel.loadPendingShipments() }
                                ) {
                                    Text("Assign Shipments")
                                }
                            }
                        }
                    }

                    // Shipments list
                    if (uiState.shipments.isEmpty()) {
                        item {
                            Card(
                                modifier = Modifier.fillMaxWidth()
                            ) {
                                Column(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .padding(32.dp),
                                    horizontalAlignment = Alignment.CenterHorizontally
                                ) {
                                    Text(
                                        text = "No shipments assigned",
                                        style = MaterialTheme.typography.titleMedium
                                    )
                                    if (uiState.isEditMode && uiState.cargo.canAcceptShipments()) {
                                        Text(
                                            text = "Assign shipments to this cargo container",
                                            style = MaterialTheme.typography.bodyMedium,
                                            color = MaterialTheme.colorScheme.onSurfaceVariant
                                        )
                                        Spacer(modifier = Modifier.height(16.dp))
                                        Button(
                                            onClick = { viewModel.loadPendingShipments() }
                                        ) {
                                            Text("Assign Shipments")
                                        }
                                    }
                                }
                            }
                        }
                    } else {
                        items(uiState.shipments) { shipment ->
                            ShipmentItem(
                                shipment = shipment,
                                isEditMode = uiState.isEditMode,
                                onRemoveShipment = {
                                    viewModel.removeShipment(shipment.id)
                                }
                            )
                        }
                    }

                    // Utilization summary
                    item {
                        CargoUtilizationSection(cargo = uiState.cargo)
                    }
                }
            }
        }
    }
}

@Composable
private fun CargoStatusSection(
    cargo: com.tfkcolin.joceladmin.data.models.Cargo,
    isEditMode: Boolean,
    onProgressStatus: () -> Unit,
    onRegressStatus: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "Status",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column {
                    Text(
                        text = cargo.getStatus().label,
                        style = MaterialTheme.typography.headlineSmall,
                        fontWeight = FontWeight.Bold
                    )
                    Text(
                        text = cargo.getStatus().description,
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
                
                if (isEditMode) {
                    Row {
                        if (cargo.canRegress()) {
                            OutlinedButton(onClick = onRegressStatus) {
                                Text("Previous")
                            }
                        }
                        
                        Spacer(modifier = Modifier.width(8.dp))
                        
                        if (cargo.canProgress()) {
                            Button(onClick = onProgressStatus) {
                                Text("Next")
                            }
                        }
                    }
                }
            }
        }
    }
}

@Composable
private fun CargoInfoSection(
    cargo: com.tfkcolin.joceladmin.data.models.Cargo,
    isEditMode: Boolean,
    onCargoInfoUpdate: (String, String, String, String, String) -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "Cargo Information",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )

            Spacer(modifier = Modifier.height(16.dp))

            if (isEditMode) {
                // Editable cargo form
                var origin by remember { mutableStateOf(cargo.origin) }
                var destination by remember { mutableStateOf(cargo.destination) }
                var carrier by remember { mutableStateOf(cargo.carrier) }
                var vesselName by remember { mutableStateOf(cargo.vesselName) }
                var route by remember { mutableStateOf(cargo.route) }

                LaunchedEffect(origin, destination, carrier, vesselName, route) {
                    onCargoInfoUpdate(origin, destination, carrier, vesselName, route)
                }

                OutlinedTextField(
                    value = origin,
                    onValueChange = { origin = it },
                    label = { Text("Origin *") },
                    modifier = Modifier.fillMaxWidth()
                )

                Spacer(modifier = Modifier.height(8.dp))

                OutlinedTextField(
                    value = destination,
                    onValueChange = { destination = it },
                    label = { Text("Destination *") },
                    modifier = Modifier.fillMaxWidth()
                )

                Spacer(modifier = Modifier.height(8.dp))

                OutlinedTextField(
                    value = carrier,
                    onValueChange = { carrier = it },
                    label = { Text("Carrier") },
                    modifier = Modifier.fillMaxWidth()
                )

                Spacer(modifier = Modifier.height(8.dp))

                OutlinedTextField(
                    value = vesselName,
                    onValueChange = { vesselName = it },
                    label = { Text("Vessel Name") },
                    modifier = Modifier.fillMaxWidth()
                )

                Spacer(modifier = Modifier.height(8.dp))

                OutlinedTextField(
                    value = route,
                    onValueChange = { route = it },
                    label = { Text("Route Description") },
                    modifier = Modifier.fillMaxWidth(),
                    maxLines = 2
                )
            } else {
                // Read-only cargo display
                Text(
                    text = cargo.getRouteDescription(),
                    style = MaterialTheme.typography.headlineSmall,
                    fontWeight = FontWeight.Bold
                )

                if (cargo.carrier.isNotBlank()) {
                    Text(
                        text = "Carrier: ${cargo.carrier}",
                        style = MaterialTheme.typography.bodyMedium
                    )
                }

                if (cargo.vesselName.isNotBlank()) {
                    Text(
                        text = "Vessel: ${cargo.vesselName}",
                        style = MaterialTheme.typography.bodyMedium
                    )
                }

                if (cargo.currentLocation.isNotBlank()) {
                    Text(
                        text = "Current Location: ${cargo.currentLocation}",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.primary
                    )
                }
            }
        }
    }
}
