package com.tfkcolin.joceladmin.ui.screens.financial

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.tfkcolin.joceladmin.data.models.CountryData
import com.tfkcolin.joceladmin.data.models.FinancialTransaction
import com.tfkcolin.joceladmin.data.models.TransactionType
import com.tfkcolin.joceladmin.repository.CountryRepository
import com.tfkcolin.joceladmin.repository.FinancialRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * ViewModel for financial management screens
 * Handles financial transactions, countries, and reporting
 */
@HiltViewModel
class FinancialViewModel @Inject constructor(
    private val financialRepository: FinancialRepository,
    private val countryRepository: CountryRepository
) : ViewModel() {

    private val _uiState = MutableStateFlow(FinancialUiState())
    val uiState: StateFlow<FinancialUiState> = _uiState.asStateFlow()

    init {
        loadTransactions()
        loadCountries()
        loadStatistics()
    }

    /**
     * Load transactions based on current filters
     */
    fun loadTransactions() {
        _uiState.value = _uiState.value.copy(isLoading = true, errorMessage = null)

        viewModelScope.launch {
            val result = when {
                _uiState.value.selectedCountry.isNotBlank() && _uiState.value.selectedType != null -> {
                    financialRepository.getTransactionsByType(
                        _uiState.value.selectedType!!,
                        _uiState.value.selectedCountry
                    )
                }
                _uiState.value.selectedCountry.isNotBlank() -> {
                    financialRepository.getTransactionsByCountry(_uiState.value.selectedCountry)
                }
                _uiState.value.selectedType != null -> {
                    financialRepository.getTransactionsByType(_uiState.value.selectedType!!)
                }
                _uiState.value.searchQuery.isNotBlank() -> {
                    financialRepository.searchTransactionsByLabel(_uiState.value.searchQuery)
                }
                else -> {
                    financialRepository.getRecentTransactions(50)
                }
            }

            result.fold(
                onSuccess = { transactions ->
                    _uiState.value = _uiState.value.copy(
                        transactions = transactions,
                        isLoading = false,
                        errorMessage = null
                    )
                },
                onFailure = { exception ->
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        errorMessage = exception.message ?: "Failed to load transactions"
                    )
                }
            )
        }
    }

    /**
     * Load countries
     */
    private fun loadCountries() {
        viewModelScope.launch {
            countryRepository.getActiveCountries().fold(
                onSuccess = { countries ->
                    _uiState.value = _uiState.value.copy(countries = countries)
                },
                onFailure = { /* Ignore country loading errors */ }
            )
        }
    }

    /**
     * Load financial statistics
     */
    private fun loadStatistics() {
        viewModelScope.launch {
            financialRepository.getFinancialStatistics().fold(
                onSuccess = { stats ->
                    _uiState.value = _uiState.value.copy(statistics = stats)
                },
                onFailure = { /* Ignore statistics errors */ }
            )
        }
    }

    /**
     * Filter transactions by country
     */
    fun filterByCountry(country: String) {
        _uiState.value = _uiState.value.copy(
            selectedCountry = country,
            searchQuery = ""
        )
        loadTransactions()
        loadCountrySummary(country)
    }

    /**
     * Filter transactions by type
     */
    fun filterByType(type: TransactionType?) {
        _uiState.value = _uiState.value.copy(
            selectedType = type,
            searchQuery = ""
        )
        loadTransactions()
    }

    /**
     * Search transactions
     */
    fun searchTransactions(query: String) {
        _uiState.value = _uiState.value.copy(
            searchQuery = query,
            selectedCountry = "",
            selectedType = null
        )
        if (query.length >= 2) {
            loadTransactions()
        } else if (query.isEmpty()) {
            loadTransactions()
        }
    }

    /**
     * Clear all filters
     */
    fun clearFilters() {
        _uiState.value = _uiState.value.copy(
            selectedCountry = "",
            selectedType = null,
            searchQuery = ""
        )
        loadTransactions()
    }

    /**
     * Load country financial summary
     */
    private fun loadCountrySummary(country: String) {
        if (country.isBlank()) return
        
        viewModelScope.launch {
            financialRepository.getFinancialSummaryByCountry(country).fold(
                onSuccess = { summary ->
                    _uiState.value = _uiState.value.copy(countrySummary = summary)
                },
                onFailure = { /* Ignore summary errors */ }
            )
        }
    }

    /**
     * Create new transaction
     */
    fun createTransaction(transaction: FinancialTransaction) {
        viewModelScope.launch {
            financialRepository.createTransaction(transaction).fold(
                onSuccess = {
                    loadTransactions()
                    loadStatistics()
                    _uiState.value = _uiState.value.copy(
                        showCreateDialog = false
                    )
                },
                onFailure = { exception ->
                    _uiState.value = _uiState.value.copy(
                        errorMessage = "Failed to create transaction: ${exception.message}"
                    )
                }
            )
        }
    }

    /**
     * Mark/unmark transaction
     */
    fun toggleTransactionMark(transactionId: String, marked: Boolean) {
        viewModelScope.launch {
            financialRepository.markTransaction(transactionId, marked).fold(
                onSuccess = {
                    loadTransactions()
                },
                onFailure = { exception ->
                    _uiState.value = _uiState.value.copy(
                        errorMessage = "Failed to update transaction: ${exception.message}"
                    )
                }
            )
        }
    }

    /**
     * Show create transaction dialog
     */
    fun showCreateDialog() {
        _uiState.value = _uiState.value.copy(showCreateDialog = true)
    }

    /**
     * Hide create transaction dialog
     */
    fun hideCreateDialog() {
        _uiState.value = _uiState.value.copy(showCreateDialog = false)
    }

    /**
     * Refresh all data
     */
    fun refresh() {
        loadTransactions()
        loadCountries()
        loadStatistics()
        if (_uiState.value.selectedCountry.isNotBlank()) {
            loadCountrySummary(_uiState.value.selectedCountry)
        }
    }

    /**
     * Clear error message
     */
    fun clearError() {
        _uiState.value = _uiState.value.copy(errorMessage = null)
    }

    /**
     * Get transactions by date range
     */
    fun loadTransactionsByDateRange(startDate: Long, endDate: Long, country: String? = null) {
        viewModelScope.launch {
            financialRepository.getTransactionsByDateRange(startDate, endDate, country).fold(
                onSuccess = { transactions ->
                    _uiState.value = _uiState.value.copy(
                        transactions = transactions,
                        isLoading = false
                    )
                },
                onFailure = { exception ->
                    _uiState.value = _uiState.value.copy(
                        errorMessage = "Failed to load transactions: ${exception.message}"
                    )
                }
            )
        }
    }
}

/**
 * UI state for financial screens
 */
data class FinancialUiState(
    val transactions: List<FinancialTransaction> = emptyList(),
    val countries: List<CountryData> = emptyList(),
    val isLoading: Boolean = false,
    val errorMessage: String? = null,
    val selectedCountry: String = "",
    val selectedType: TransactionType? = null,
    val searchQuery: String = "",
    val statistics: Map<String, Any> = emptyMap(),
    val countrySummary: Map<String, Double> = emptyMap(),
    val showCreateDialog: Boolean = false
)
