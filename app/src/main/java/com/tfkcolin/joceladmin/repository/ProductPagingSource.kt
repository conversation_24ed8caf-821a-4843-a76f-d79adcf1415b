package com.tfkcolin.joceladmin.repository

import androidx.paging.PagingSource
import androidx.paging.PagingState
import com.google.firebase.firestore.DocumentSnapshot
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.Query
import com.tfkcolin.joceladmin.data.models.Product
import com.tfkcolin.joceladmin.data.models.ProductSearchFilters
import com.tfkcolin.joceladmin.data.models.ProductSortBy
import com.tfkcolin.joceladmin.data.models.SortOrder
import kotlinx.coroutines.tasks.await

/**
 * Paging source for loading products from Firestore with filtering and sorting support
 */
class ProductPagingSource(
    private val firestore: FirebaseFirestore,
    private val filters: ProductSearchFilters
) : PagingSource<DocumentSnapshot, Product>() {

    companion object {
        private const val COLLECTION_PRODUCTS = "products"
    }

    override suspend fun load(params: LoadParams<DocumentSnapshot>): LoadResult<DocumentSnapshot, Product> {
        return try {
            val query = buildQuery()
            
            val querySnapshot = if (params.key == null) {
                // First page
                query.limit(params.loadSize.toLong()).get().await()
            } else {
                // Subsequent pages
                query.startAfter(params.key!!).limit(params.loadSize.toLong()).get().await()
            }
            
            val products = querySnapshot.toObjects(Product::class.java)
            val lastDocument = querySnapshot.documents.lastOrNull()
            
            LoadResult.Page(
                data = products,
                prevKey = null, // Only support forward pagination
                nextKey = lastDocument
            )
        } catch (e: Exception) {
            LoadResult.Error(e)
        }
    }

    override fun getRefreshKey(state: PagingState<DocumentSnapshot, Product>): DocumentSnapshot? {
        return null
    }

    /**
     * Build Firestore query based on filters and sorting
     */
    private fun buildQuery(): Query {
        var query: Query = firestore.collection(COLLECTION_PRODUCTS)
        
        // Apply filters
        if (filters.categories.isNotEmpty()) {
            query = query.whereIn("category", filters.categories)
        }
        
        if (filters.genres.isNotEmpty()) {
            query = query.whereIn("genre", filters.genres)
        }
        
        if (filters.brands.isNotEmpty()) {
            query = query.whereIn("brand", filters.brands)
        }
        
        if (filters.suppliers.isNotEmpty()) {
            query = query.whereIn("supplier", filters.suppliers)
        }
        
        if (filters.isActive != null) {
            query = query.whereEqualTo("isActive", filters.isActive)
        }
        
        if (filters.isAvailable != null) {
            query = query.whereEqualTo("isAvailable", filters.isAvailable)
        }
        
        if (filters.isFeatured != null) {
            query = query.whereEqualTo("isFeatured", filters.isFeatured)
        }
        
        if (filters.hasImages != null) {
            if (filters.hasImages == true) {
                query = query.whereGreaterThan("images", emptyList<String>())
            } else {
                query = query.whereEqualTo("images", emptyList<String>())
            }
        }
        
        // Apply price range filter
        filters.priceRange?.let { priceRange ->
            query = query.whereGreaterThanOrEqualTo("price", priceRange.minPrice)
                .whereLessThanOrEqualTo("price", priceRange.maxPrice)
        }
        
        // Apply stock range filter
        filters.stockRange?.let { stockRange ->
            query = query.whereGreaterThanOrEqualTo("stock", stockRange.minStock)
                .whereLessThanOrEqualTo("stock", stockRange.maxStock)
        }
        
        // Apply date range filter
        filters.dateRange?.let { dateRange ->
            query = query.whereGreaterThanOrEqualTo("createdAt", dateRange.startDate)
                .whereLessThanOrEqualTo("createdAt", dateRange.endDate)
        }
        
        // Apply tags filter (array-contains-any for multiple tags)
        if (filters.tags.isNotEmpty()) {
            query = query.whereArrayContainsAny("tags", filters.tags)
        }
        
        // Apply sorting
        val sortField = when (filters.sortBy) {
            ProductSortBy.NAME -> "name"
            ProductSortBy.PRICE -> "price"
            ProductSortBy.STOCK -> "stock"
            ProductSortBy.CREATED_DATE -> "createdAt"
            ProductSortBy.UPDATED_DATE -> "updatedAt"
            ProductSortBy.VIEW_COUNT -> "analytics.viewCount"
            ProductSortBy.ORDER_COUNT -> "analytics.orderCount"
            ProductSortBy.RATING -> "analytics.averageRating"
        }
        
        val sortDirection = when (filters.sortOrder) {
            SortOrder.ASC -> Query.Direction.ASCENDING
            SortOrder.DESC -> Query.Direction.DESCENDING
        }
        
        query = query.orderBy(sortField, sortDirection)
        
        return query
    }
}
