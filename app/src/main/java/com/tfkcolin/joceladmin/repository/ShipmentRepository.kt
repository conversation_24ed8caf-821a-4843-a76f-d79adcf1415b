package com.tfkcolin.joceladmin.repository

import com.google.firebase.firestore.CollectionReference
import com.google.firebase.firestore.Query
import com.tfkcolin.joceladmin.data.models.Shipment
import com.tfkcolin.joceladmin.data.models.ShipmentStatus
import com.tfkcolin.joceladmin.repository.base.BaseRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.tasks.await
import javax.inject.Inject
import javax.inject.Named
import javax.inject.Singleton

/**
 * Repository for shipment operations
 * Handles CRUD operations for individual shipments within cargo containers
 */
@Singleton
class ShipmentRepository @Inject constructor(
    @Named("SHIPMENT_DB") private val shipmentCollection: CollectionReference
) : BaseRepository() {

    /**
     * Create a new shipment
     */
    suspend fun createShipment(shipment: Shipment): Result<Shipment> = safeCall {
        validateRequired("shipment" to shipment)
        
        val shipmentId = if (shipment.id.isBlank()) generateId() else shipment.id
        val trackingNumber = generateTrackingNumber()
        
        val newShipment = shipment.copy(
            id = shipmentId,
            trackingNumber = trackingNumber,
            created = getCurrentTimestamp(),
            updatedAt = getCurrentTimestamp()
        )

        shipmentCollection.document(shipmentId).set(newShipment).await()
        newShipment
    }

    /**
     * Get shipment by ID
     */
    suspend fun getShipmentById(shipmentId: String): Result<Shipment> = safeCall {
        validateRequired("shipmentId" to shipmentId)
        
        val document = shipmentCollection.document(shipmentId).get().await()
        document.toObject(Shipment::class.java) ?: throw Exception("Shipment not found")
    }

    /**
     * Update existing shipment
     */
    suspend fun updateShipment(shipment: Shipment): Result<Shipment> = safeCall {
        validateRequired("shipment" to shipment, "shipmentId" to shipment.id)
        
        val updatedShipment = shipment.copy(
            updatedAt = getCurrentTimestamp()
        )

        shipmentCollection.document(shipment.id).set(updatedShipment).await()
        updatedShipment
    }

    /**
     * Delete shipment
     */
    suspend fun deleteShipment(shipmentId: String): Result<Unit> = safeCall {
        validateRequired("shipmentId" to shipmentId)
        shipmentCollection.document(shipmentId).delete().await()
    }

    /**
     * Get shipments by cargo ID
     */
    suspend fun getShipmentsByCargoId(cargoId: String, limit: Int = 50): Result<List<Shipment>> = safeCall {
        validateRequired("cargoId" to cargoId)
        
        val query = shipmentCollection
            .whereEqualTo("cargoId", cargoId)
            .orderBy("created", Query.Direction.DESCENDING)
            .limit(limit.toLong())

        val snapshot = query.get().await()
        snapshot.toObjects(Shipment::class.java)
    }

    /**
     * Get shipments by status
     */
    suspend fun getShipmentsByStatus(status: ShipmentStatus, limit: Int = 50): Result<List<Shipment>> = safeCall {
        val query = shipmentCollection
            .whereEqualTo("statusIndex", status.ordinal)
            .orderBy("created", Query.Direction.DESCENDING)
            .limit(limit.toLong())

        val snapshot = query.get().await()
        snapshot.toObjects(Shipment::class.java)
    }

    /**
     * Get shipments by client name
     */
    suspend fun searchShipmentsByClient(clientName: String, limit: Int = 50): Result<List<Shipment>> = safeCall {
        validateRequired("clientName" to clientName)
        
        val query = shipmentCollection
            .whereGreaterThanOrEqualTo("clientName", clientName)
            .whereLessThanOrEqualTo("clientName", clientName + '\uf8ff')
            .orderBy("clientName")
            .limit(limit.toLong())

        val snapshot = query.get().await()
        snapshot.toObjects(Shipment::class.java)
    }

    /**
     * Get shipments by tracking number
     */
    suspend fun getShipmentByTrackingNumber(trackingNumber: String): Result<Shipment?> = safeCall {
        validateRequired("trackingNumber" to trackingNumber)
        
        val query = shipmentCollection
            .whereEqualTo("trackingNumber", trackingNumber)
            .limit(1)

        val snapshot = query.get().await()
        snapshot.toObjects(Shipment::class.java).firstOrNull()
    }

    /**
     * Get pending shipments (not assigned to cargo)
     */
    suspend fun getPendingShipments(limit: Int = 50): Result<List<Shipment>> = safeCall {
        val query = shipmentCollection
            .whereEqualTo("statusIndex", ShipmentStatus.PENDING.ordinal)
            .whereEqualTo("cargoId", "")
            .orderBy("created", Query.Direction.DESCENDING)
            .limit(limit.toLong())

        val snapshot = query.get().await()
        snapshot.toObjects(Shipment::class.java)
    }

    /**
     * Get recent shipments
     */
    suspend fun getRecentShipments(limit: Int = 20): Result<List<Shipment>> = safeCall {
        val query = shipmentCollection
            .orderBy("created", Query.Direction.DESCENDING)
            .limit(limit.toLong())

        val snapshot = query.get().await()
        snapshot.toObjects(Shipment::class.java)
    }

    /**
     * Get shipments flow for real-time updates
     */
    fun getShipmentsFlow(): Flow<List<Shipment>> = flow {
        val query = shipmentCollection.orderBy("created", Query.Direction.DESCENDING)
        
        query.addSnapshotListener { snapshot, error ->
            if (error == null && snapshot != null) {
                val shipments = snapshot.toObjects(Shipment::class.java)
                // Note: In a real implementation, you'd use callbackFlow for proper flow emission
            }
        }
    }

    /**
     * Update shipment status
     */
    suspend fun updateShipmentStatus(shipmentId: String, status: ShipmentStatus): Result<Shipment> = safeCall {
        validateRequired("shipmentId" to shipmentId)
        
        val shipment = getShipmentById(shipmentId).getOrThrow()
        val updatedShipment = shipment.updateStatus(status)
        
        shipmentCollection.document(shipmentId).set(updatedShipment).await()
        updatedShipment
    }

    /**
     * Assign shipment to cargo
     */
    suspend fun assignShipmentToCargo(shipmentId: String, cargoId: String): Result<Shipment> = safeCall {
        validateRequired("shipmentId" to shipmentId, "cargoId" to cargoId)
        
        val shipment = getShipmentById(shipmentId).getOrThrow()
        
        if (!shipment.canAssignToCargo()) {
            throw Exception("Shipment cannot be assigned to cargo in current status")
        }
        
        val updatedShipment = shipment.copy(
            cargoId = cargoId,
            statusIndex = ShipmentStatus.LOADED.ordinal,
            updatedAt = getCurrentTimestamp()
        )
        
        shipmentCollection.document(shipmentId).set(updatedShipment).await()
        updatedShipment
    }

    /**
     * Remove shipment from cargo
     */
    suspend fun removeShipmentFromCargo(shipmentId: String): Result<Shipment> = safeCall {
        validateRequired("shipmentId" to shipmentId)
        
        val shipment = getShipmentById(shipmentId).getOrThrow()
        val updatedShipment = shipment.copy(
            cargoId = "",
            statusIndex = ShipmentStatus.PENDING.ordinal,
            updatedAt = getCurrentTimestamp()
        )
        
        shipmentCollection.document(shipmentId).set(updatedShipment).await()
        updatedShipment
    }

    /**
     * Mark shipment as delivered
     */
    suspend fun markShipmentAsDelivered(shipmentId: String): Result<Shipment> = safeCall {
        validateRequired("shipmentId" to shipmentId)
        
        val shipment = getShipmentById(shipmentId).getOrThrow()
        val updatedShipment = shipment.copy(
            statusIndex = ShipmentStatus.DELIVERED.ordinal,
            actualDeliveryDate = getCurrentTimestamp(),
            updatedAt = getCurrentTimestamp()
        )
        
        shipmentCollection.document(shipmentId).set(updatedShipment).await()
        updatedShipment
    }

    /**
     * Get shipment statistics
     */
    suspend fun getShipmentStatistics(): Result<Map<String, Int>> = safeCall {
        val allShipments = shipmentCollection.get().await().toObjects(Shipment::class.java)
        
        mapOf(
            "total" to allShipments.size,
            "pending" to allShipments.count { it.getStatus() == ShipmentStatus.PENDING },
            "loaded" to allShipments.count { it.getStatus() == ShipmentStatus.LOADED },
            "inTransit" to allShipments.count { it.getStatus() == ShipmentStatus.IN_TRANSIT },
            "delivered" to allShipments.count { it.getStatus() == ShipmentStatus.DELIVERED },
            "unassigned" to allShipments.count { it.cargoId.isBlank() }
        )
    }

    /**
     * Generate unique tracking number
     */
    private fun generateTrackingNumber(): String {
        val timestamp = System.currentTimeMillis()
        val random = (1000..9999).random()
        return "SHP-$timestamp-$random"
    }
}
