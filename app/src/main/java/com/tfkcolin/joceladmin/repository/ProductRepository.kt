package com.tfkcolin.joceladmin.repository

import androidx.paging.Pager
import androidx.paging.PagingConfig
import androidx.paging.PagingData
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.Query
import com.tfkcolin.joceladmin.data.models.*
import com.tfkcolin.joceladmin.repository.base.BaseRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.tasks.await
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Repository for managing product operations
 */
@Singleton
class ProductRepository @Inject constructor(
    private val firestore: FirebaseFirestore
) : BaseRepository() {

    companion object {
        private const val COLLECTION_PRODUCTS = "products"
        private const val PAGE_SIZE = 20
    }

    /**
     * Get paginated products with optional filters
     */
    fun getProducts(filters: ProductSearchFilters = ProductSearchFilters()): Flow<PagingData<Product>> {
        return Pager(
            config = PagingConfig(
                pageSize = PAGE_SIZE,
                enablePlaceholders = false
            ),
            pagingSourceFactory = {
                ProductPagingSource(
                    firestore = firestore,
                    filters = filters
                )
            }
        ).flow
    }

    /**
     * Get product by ID
     */
    suspend fun getProductById(id: String): Result<Product?> = safeCall {
        validateRequired("id" to id)
        
        val document = firestore.collection(COLLECTION_PRODUCTS)
            .document(id)
            .get()
            .await()
        
        document.toObject(Product::class.java)
    }

    /**
     * Get products by IDs
     */
    suspend fun getProductsByIds(ids: List<String>): Result<List<Product>> = safeCall {
        if (ids.isEmpty()) return@safeCall emptyList()
        
        val chunks = ids.chunked(10) // Firestore 'in' query limit
        val allProducts = mutableListOf<Product>()
        
        chunks.forEach { chunk ->
            val documents = firestore.collection(COLLECTION_PRODUCTS)
                .whereIn("id", chunk)
                .get()
                .await()
            
            documents.documents.mapNotNull { 
                it.toObject(Product::class.java) 
            }.let { allProducts.addAll(it) }
        }
        
        allProducts
    }

    /**
     * Create new product
     */
    suspend fun createProduct(request: ProductCreateRequest): Result<Product> = safeCall {
        validateRequired(
            "name" to request.name,
            "category" to request.category,
            "price" to request.price
        )
        validatePositive(request.price, "price")
        validatePositive(request.cost, "cost")
        validatePositive(request.stock, "stock")
        
        val id = generateId()
        val timestamp = getCurrentTimestamp()
        
        val product = Product(
            id = id,
            name = request.name,
            description = request.description,
            sku = request.sku.ifEmpty { generateSku(request.name, request.category) },
            category = request.category,
            genre = request.genre,
            brand = request.brand,
            supplier = request.supplier,
            price = request.price,
            cost = request.cost,
            currency = request.currency,
            weight = request.weight,
            dimensions = request.dimensions,
            stock = request.stock,
            minStock = request.minStock,
            maxStock = request.maxStock,
            tags = request.tags,
            specifications = request.specifications,
            seoData = request.seoData,
            createdAt = timestamp,
            updatedAt = timestamp,
            isActive = true,
            isAvailable = true
        )
        
        firestore.collection(COLLECTION_PRODUCTS)
            .document(id)
            .set(product)
            .await()
        
        product
    }

    /**
     * Update product
     */
    suspend fun updateProduct(request: ProductUpdateRequest): Result<Product> = safeCall {
        validateRequired("id" to request.id)
        
        val updates = mutableMapOf<String, Any>()
        
        request.name?.let { updates["name"] = it }
        request.description?.let { updates["description"] = it }
        request.sku?.let { updates["sku"] = it }
        request.category?.let { updates["category"] = it }
        request.genre?.let { updates["genre"] = it }
        request.brand?.let { updates["brand"] = it }
        request.supplier?.let { updates["supplier"] = it }
        request.price?.let { 
            validatePositive(it, "price")
            updates["price"] = it 
        }
        request.cost?.let { 
            validatePositive(it, "cost")
            updates["cost"] = it 
        }
        request.currency?.let { updates["currency"] = it }
        request.weight?.let { updates["weight"] = it }
        request.dimensions?.let { updates["dimensions"] = it }
        request.stock?.let { 
            validatePositive(it, "stock")
            updates["stock"] = it 
        }
        request.minStock?.let { 
            validatePositive(it, "minStock")
            updates["minStock"] = it 
        }
        request.maxStock?.let { 
            validatePositive(it, "maxStock")
            updates["maxStock"] = it 
        }
        request.isActive?.let { updates["isActive"] = it }
        request.isAvailable?.let { updates["isAvailable"] = it }
        request.isFeatured?.let { updates["isFeatured"] = it }
        request.tags?.let { updates["tags"] = it }
        request.specifications?.let { updates["specifications"] = it }
        request.seoData?.let { updates["seoData"] = it }
        
        updates["updatedAt"] = getCurrentTimestamp()
        updates["version"] = com.google.firebase.firestore.FieldValue.increment(1)
        
        firestore.collection(COLLECTION_PRODUCTS)
            .document(request.id)
            .update(updates)
            .await()
        
        // Return updated product
        getProductById(request.id).getOrThrow()!!
    }

    /**
     * Delete product
     */
    suspend fun deleteProduct(id: String): Result<Boolean> = safeCall {
        validateRequired("id" to id)
        
        firestore.collection(COLLECTION_PRODUCTS)
            .document(id)
            .delete()
            .await()
        
        true
    }

    /**
     * Add image to product
     */
    suspend fun addImageToProduct(productId: String, imageId: String, isPrimary: Boolean = false): Result<Product> = safeCall {
        validateRequired("productId" to productId, "imageId" to imageId)
        
        val product = getProductById(productId).getOrThrow()
            ?: throw IllegalArgumentException("Product not found")
        
        val updatedImages = product.images.toMutableList()
        if (!updatedImages.contains(imageId)) {
            updatedImages.add(imageId)
        }
        
        val updates = mutableMapOf<String, Any>(
            "images" to updatedImages,
            "updatedAt" to getCurrentTimestamp()
        )
        
        if (isPrimary || product.primaryImageId.isEmpty()) {
            updates["primaryImageId"] = imageId
        }
        
        firestore.collection(COLLECTION_PRODUCTS)
            .document(productId)
            .update(updates)
            .await()
        
        getProductById(productId).getOrThrow()!!
    }

    /**
     * Remove image from product
     */
    suspend fun removeImageFromProduct(productId: String, imageId: String): Result<Product> = safeCall {
        validateRequired("productId" to productId, "imageId" to imageId)
        
        val product = getProductById(productId).getOrThrow()
            ?: throw IllegalArgumentException("Product not found")
        
        val updatedImages = product.images.toMutableList()
        updatedImages.remove(imageId)
        
        val updates = mutableMapOf<String, Any>(
            "images" to updatedImages,
            "updatedAt" to getCurrentTimestamp()
        )
        
        // If removing primary image, set new primary
        if (product.primaryImageId == imageId) {
            updates["primaryImageId"] = updatedImages.firstOrNull() ?: ""
        }
        
        firestore.collection(COLLECTION_PRODUCTS)
            .document(productId)
            .update(updates)
            .await()
        
        getProductById(productId).getOrThrow()!!
    }

    /**
     * Get products by category
     */
    suspend fun getProductsByCategory(category: String, limit: Int = 50): Result<List<Product>> = safeCall {
        validateRequired("category" to category)
        
        val documents = firestore.collection(COLLECTION_PRODUCTS)
            .whereEqualTo("category", category)
            .whereEqualTo("isActive", true)
            .orderBy("name")
            .limit(limit.toLong())
            .get()
            .await()
        
        documents.toObjects(Product::class.java)
    }

    /**
     * Get featured products
     */
    suspend fun getFeaturedProducts(limit: Int = 20): Result<List<Product>> = safeCall {
        val documents = firestore.collection(COLLECTION_PRODUCTS)
            .whereEqualTo("isFeatured", true)
            .whereEqualTo("isActive", true)
            .whereEqualTo("isAvailable", true)
            .orderBy("analytics.viewCount", Query.Direction.DESCENDING)
            .limit(limit.toLong())
            .get()
            .await()
        
        documents.toObjects(Product::class.java)
    }

    /**
     * Search products by text
     */
    suspend fun searchProducts(query: String, limit: Int = 50): Result<List<Product>> = safeCall {
        validateRequired("query" to query)
        
        // Note: This is a basic implementation. For production, consider using
        // Algolia or Elasticsearch for better full-text search capabilities
        val documents = firestore.collection(COLLECTION_PRODUCTS)
            .whereEqualTo("isActive", true)
            .orderBy("name")
            .limit(limit.toLong())
            .get()
            .await()
        
        val allProducts = documents.toObjects(Product::class.java)
        
        // Filter by query in name, description, sku, or tags
        allProducts.filter { product ->
            query.lowercase() in product.name.lowercase() ||
            query.lowercase() in product.description.lowercase() ||
            query.lowercase() in product.sku.lowercase() ||
            product.tags.any { tag -> query.lowercase() in tag.lowercase() }
        }
    }

    /**
     * Get product statistics
     */
    suspend fun getProductStats(): Result<ProductStats> = safeCall {
        val documents = firestore.collection(COLLECTION_PRODUCTS)
            .get()
            .await()
        
        val products = documents.toObjects(Product::class.java)
        
        val activeProducts = products.count { it.isActive }
        val availableProducts = products.count { it.isAvailable }
        val featuredProducts = products.count { it.isFeatured }
        val lowStockProducts = products.count { it.stock <= it.minStock && it.minStock > 0 }
        val outOfStockProducts = products.count { it.stock == 0 }
        
        val categoryCount = products.groupingBy { it.category }.eachCount()
        val genreCount = products.groupingBy { it.genre }.eachCount()
        val brandCount = products.groupingBy { it.brand }.eachCount()
        
        val averagePrice = if (products.isNotEmpty()) products.map { it.price }.average() else 0.0
        val totalValue = products.sumOf { it.price * it.stock }
        
        ProductStats(
            totalProducts = products.size,
            activeProducts = activeProducts,
            availableProducts = availableProducts,
            featuredProducts = featuredProducts,
            lowStockProducts = lowStockProducts,
            outOfStockProducts = outOfStockProducts,
            productsByCategory = categoryCount,
            productsByGenre = genreCount,
            productsByBrand = brandCount,
            averagePrice = averagePrice,
            totalValue = totalValue
        )
    }

    /**
     * Generate SKU for product
     */
    private fun generateSku(name: String, category: String): String {
        val namePrefix = name.take(3).uppercase().replace(Regex("[^A-Z]"), "")
        val categoryPrefix = category.take(2).uppercase().replace(Regex("[^A-Z]"), "")
        val timestamp = System.currentTimeMillis().toString().takeLast(6)
        return "$categoryPrefix$namePrefix$timestamp"
    }
}
