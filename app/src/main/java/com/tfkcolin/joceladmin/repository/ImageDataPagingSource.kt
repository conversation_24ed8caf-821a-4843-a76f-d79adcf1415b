package com.tfkcolin.joceladmin.repository

import androidx.paging.PagingSource
import androidx.paging.PagingState
import com.google.firebase.firestore.DocumentSnapshot
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.Query
import com.google.firebase.firestore.QuerySnapshot
import com.tfkcolin.joceladmin.data.models.ImageData
import com.tfkcolin.joceladmin.data.models.ImageSearchFilters
import kotlinx.coroutines.tasks.await

/**
 * Paging source for loading images from Firestore with filtering support
 */
class ImageDataPagingSource(
    private val firestore: FirebaseFirestore,
    private val filters: ImageSearchFilters
) : PagingSource<DocumentSnapshot, ImageData>() {

    companion object {
        private const val COLLECTION_IMAGES = "images"
        private const val PAGE_SIZE = 20
    }

    override suspend fun load(params: LoadParams<DocumentSnapshot>): LoadResult<DocumentSnapshot, ImageData> {
        return try {
            val query = buildQuery()
            
            val querySnapshot = if (params.key == null) {
                // First page
                query.limit(params.loadSize.toLong()).get().await()
            } else {
                // Subsequent pages
                query.startAfter(params.key!!).limit(params.loadSize.toLong()).get().await()
            }
            
            val images = querySnapshot.toObjects(ImageData::class.java)
            val lastDocument = querySnapshot.documents.lastOrNull()
            
            LoadResult.Page(
                data = images,
                prevKey = null, // Only support forward pagination
                nextKey = lastDocument
            )
        } catch (e: Exception) {
            LoadResult.Error(e)
        }
    }

    override fun getRefreshKey(state: PagingState<DocumentSnapshot, ImageData>): DocumentSnapshot? {
        return null
    }

    /**
     * Build Firestore query based on filters
     */
    private fun buildQuery(): Query {
        var query: Query = firestore.collection(COLLECTION_IMAGES)
        
        // Apply filters
        if (filters.categories.isNotEmpty()) {
            query = query.whereIn("category", filters.categories)
        }
        
        if (filters.genres.isNotEmpty()) {
            query = query.whereIn("genre", filters.genres)
        }
        
        if (filters.format != null) {
            query = query.whereEqualTo("format", filters.format)
        }
        
        if (filters.isActive != null) {
            query = query.whereEqualTo("isActive", filters.isActive)
        }
        
        if (filters.hasProductInfo != null) {
            if (filters.hasProductInfo == true) {
                query = query.whereNotEqualTo("productInfo", null)
            } else {
                query = query.whereEqualTo("productInfo", null)
            }
        }
        
        if (filters.uploadedBy != null) {
            query = query.whereEqualTo("uploadedBy", filters.uploadedBy)
        }
        
        // Apply date range filter
        filters.dateRange?.let { dateRange ->
            query = query.whereGreaterThanOrEqualTo("uploadedAt", dateRange.startDate)
                .whereLessThanOrEqualTo("uploadedAt", dateRange.endDate)
        }
        
        // Apply size range filter
        filters.sizeRange?.let { sizeRange ->
            sizeRange.minWidth?.let { minWidth ->
                query = query.whereGreaterThanOrEqualTo("width", minWidth)
            }
            sizeRange.maxWidth?.let { maxWidth ->
                query = query.whereLessThanOrEqualTo("width", maxWidth)
            }
            sizeRange.minHeight?.let { minHeight ->
                query = query.whereGreaterThanOrEqualTo("height", minHeight)
            }
            sizeRange.maxHeight?.let { maxHeight ->
                query = query.whereLessThanOrEqualTo("height", maxHeight)
            }
            sizeRange.minFileSize?.let { minFileSize ->
                query = query.whereGreaterThanOrEqualTo("fileSize", minFileSize)
            }
            sizeRange.maxFileSize?.let { maxFileSize ->
                query = query.whereLessThanOrEqualTo("fileSize", maxFileSize)
            }
        }
        
        // Apply tags filter (array-contains-any for multiple tags)
        if (filters.tags.isNotEmpty()) {
            query = query.whereArrayContainsAny("tags", filters.tags)
        }
        
        // Default ordering by upload date (newest first)
        query = query.orderBy("uploadedAt", Query.Direction.DESCENDING)
        
        return query
    }
}
