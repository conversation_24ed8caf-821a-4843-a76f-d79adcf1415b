package com.tfkcolin.joceladmin.repository

import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.auth.FirebaseUser
import com.google.firebase.firestore.CollectionReference
import com.tfkcolin.joceladmin.data.models.User
import com.tfkcolin.joceladmin.data.models.UserRole
import com.tfkcolin.joceladmin.repository.base.BaseRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.tasks.await
import javax.inject.Inject
import javax.inject.Named
import javax.inject.Singleton

/**
 * Repository for authentication operations
 * Implements Firebase Auth with user management
 */
@Singleton
class AuthRepository @Inject constructor(
    @Named("AUTH") private val firebaseAuth: FirebaseAuth,
    @Named("USER_DB") private val userCollection: CollectionReference
) : BaseRepository() {

    /**
     * Get current user flow
     */
    fun getCurrentUser(): Flow<FirebaseUser?> = flow {
        emit(firebaseAuth.currentUser)
    }

    /**
     * Sign in with email and password
     */
    suspend fun signInWithEmailAndPassword(
        email: String,
        password: String
    ): Result<User> = safeCall {
        validateRequired(
            "email" to email,
            "password" to password
        )
        validateEmail(email)

        val authResult = firebaseAuth.signInWithEmailAndPassword(email, password).await()
        val firebaseUser = authResult.user ?: throw Exception("Authentication failed")
        
        // Update last login time
        updateLastLoginTime(firebaseUser.uid)
        
        // Get user data from Firestore
        getUserData(firebaseUser.uid)
    }

    /**
     * Sign up with email and password
     */
    suspend fun signUpWithEmailAndPassword(
        email: String,
        password: String,
        displayName: String,
        role: UserRole = UserRole.EMPLOYEE
    ): Result<User> = safeCall {
        validateRequired(
            "email" to email,
            "password" to password,
            "displayName" to displayName
        )
        validateEmail(email)

        val authResult = firebaseAuth.createUserWithEmailAndPassword(email, password).await()
        val firebaseUser = authResult.user ?: throw Exception("User creation failed")

        // Create user profile
        val user = User(
            id = firebaseUser.uid,
            email = email,
            displayName = displayName,
            role = role,
            createdAt = getCurrentTimestamp(),
            lastLoginAt = getCurrentTimestamp()
        )

        // Save user data to Firestore
        userCollection.document(firebaseUser.uid).set(user).await()
        
        user
    }

    /**
     * Sign out current user
     */
    suspend fun signOut(): Result<Unit> = safeCall {
        firebaseAuth.signOut()
    }

    /**
     * Reset password
     */
    suspend fun resetPassword(email: String): Result<Unit> = safeCall {
        validateRequired("email" to email)
        validateEmail(email)
        
        firebaseAuth.sendPasswordResetEmail(email).await()
    }

    /**
     * Get user data from Firestore
     */
    suspend fun getUserData(userId: String): User = safeCall {
        val document = userCollection.document(userId).get().await()
        document.toObject(User::class.java) ?: throw Exception("User data not found")
    }.getOrThrow()

    /**
     * Update user profile
     */
    suspend fun updateUserProfile(user: User): Result<Unit> = safeCall {
        validateRequired(
            "id" to user.id,
            "email" to user.email,
            "displayName" to user.displayName
        )
        
        val updatedUser = user.copy(updatedAt = getCurrentTimestamp())
        userCollection.document(user.id).set(updatedUser).await()
    }

    /**
     * Update last login time
     */
    private suspend fun updateLastLoginTime(userId: String) {
        userCollection.document(userId)
            .update("lastLoginAt", getCurrentTimestamp())
            .await()
    }

    /**
     * Check if user has permission
     */
    suspend fun hasPermission(userId: String, permission: String): Boolean = safeCall {
        val user = getUserData(userId)
        user.permissions.contains(permission)
    }.getOrDefault(false)

    /**
     * Update user permissions
     */
    suspend fun updateUserPermissions(
        userId: String,
        permissions: List<String>
    ): Result<Unit> = safeCall {
        userCollection.document(userId)
            .update(
                mapOf(
                    "permissions" to permissions,
                    "updatedAt" to getCurrentTimestamp()
                )
            )
            .await()
    }

    /**
     * Deactivate user account
     */
    suspend fun deactivateUser(userId: String): Result<Unit> = safeCall {
        userCollection.document(userId)
            .update(
                mapOf(
                    "isActive" to false,
                    "updatedAt" to getCurrentTimestamp()
                )
            )
            .await()
    }

    /**
     * Activate user account
     */
    suspend fun activateUser(userId: String): Result<Unit> = safeCall {
        userCollection.document(userId)
            .update(
                mapOf(
                    "isActive" to true,
                    "updatedAt" to getCurrentTimestamp()
                )
            )
            .await()
    }

    /**
     * Get all users (admin only)
     */
    suspend fun getAllUsers(): Result<List<User>> = safeCall {
        val snapshot = userCollection.get().await()
        snapshot.toObjects(User::class.java)
    }

    /**
     * Search users by email or name
     */
    suspend fun searchUsers(query: String): Result<List<User>> = safeCall {
        val snapshot = userCollection
            .whereGreaterThanOrEqualTo("email", query)
            .whereLessThanOrEqualTo("email", query + "\uf8ff")
            .get()
            .await()
        
        val emailResults = snapshot.toObjects(User::class.java)
        
        val nameSnapshot = userCollection
            .whereGreaterThanOrEqualTo("displayName", query)
            .whereLessThanOrEqualTo("displayName", query + "\uf8ff")
            .get()
            .await()
        
        val nameResults = nameSnapshot.toObjects(User::class.java)
        
        (emailResults + nameResults).distinctBy { it.id }
    }
}
