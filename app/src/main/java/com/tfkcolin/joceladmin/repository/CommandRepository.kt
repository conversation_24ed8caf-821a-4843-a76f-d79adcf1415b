package com.tfkcolin.joceladmin.repository

import com.google.firebase.firestore.CollectionReference
import com.google.firebase.firestore.Query
import com.tfkcolin.joceladmin.data.models.Command
import com.tfkcolin.joceladmin.data.models.CommandStatus
import com.tfkcolin.joceladmin.repository.base.BaseRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.tasks.await
import javax.inject.Inject
import javax.inject.Named
import javax.inject.Singleton

/**
 * Repository for command operations
 * Handles CRUD operations for customer orders and workflow management
 */
@Singleton
class CommandRepository @Inject constructor(
    @Named("COMMAND_DB") private val commandCollection: CollectionReference
) : BaseRepository() {

    /**
     * Create a new command
     */
    suspend fun createCommand(command: Command): Result<Command> = safeCall {
        validateRequired("command" to command)
        
        val commandId = if (command.id.isBlank()) generateId() else command.id
        val commandNumber = generateCommandNumber()
        
        val newCommand = command.copy(
            id = commandId,
            commandNumber = commandNumber,
            created = getCurrentTimestamp(),
            updatedAt = getCurrentTimestamp(),
            totalSellingAmount = command.calculateTotalSellingAmount(),
            totalBuyingAmount = command.calculateTotalBuyingAmount()
        )

        commandCollection.document(commandId).set(newCommand).await()
        newCommand
    }

    /**
     * Get command by ID
     */
    suspend fun getCommandById(commandId: String): Result<Command> = safeCall {
        validateRequired("commandId" to commandId)
        
        val document = commandCollection.document(commandId).get().await()
        document.toObject(Command::class.java) ?: throw Exception("Command not found")
    }

    /**
     * Update existing command
     */
    suspend fun updateCommand(command: Command): Result<Command> = safeCall {
        validateRequired("command" to command, "commandId" to command.id)
        
        val updatedCommand = command.copy(
            updatedAt = getCurrentTimestamp(),
            totalSellingAmount = command.calculateTotalSellingAmount(),
            totalBuyingAmount = command.calculateTotalBuyingAmount()
        )

        commandCollection.document(command.id).set(updatedCommand).await()
        updatedCommand
    }

    /**
     * Delete command
     */
    suspend fun deleteCommand(commandId: String): Result<Unit> = safeCall {
        validateRequired("commandId" to commandId)
        commandCollection.document(commandId).delete().await()
    }

    /**
     * Get commands by status
     */
    suspend fun getCommandsByStatus(status: CommandStatus, limit: Int = 50): Result<List<Command>> = safeCall {
        val query = commandCollection
            .whereEqualTo("commandStepIndex", status.ordinal)
            .orderBy("created", Query.Direction.DESCENDING)
            .limit(limit.toLong())

        val snapshot = query.get().await()
        snapshot.toObjects(Command::class.java)
    }

    /**
     * Get commands by country
     */
    suspend fun getCommandsByCountry(country: String, limit: Int = 50): Result<List<Command>> = safeCall {
        validateRequired("country" to country)
        
        val query = commandCollection
            .whereEqualTo("country", country)
            .orderBy("created", Query.Direction.DESCENDING)
            .limit(limit.toLong())

        val snapshot = query.get().await()
        snapshot.toObjects(Command::class.java)
    }

    /**
     * Get commands by client name
     */
    suspend fun searchCommandsByClient(clientName: String, limit: Int = 50): Result<List<Command>> = safeCall {
        validateRequired("clientName" to clientName)
        
        val query = commandCollection
            .whereGreaterThanOrEqualTo("client.name", clientName)
            .whereLessThanOrEqualTo("client.name", clientName + '\uf8ff')
            .orderBy("client.name")
            .limit(limit.toLong())

        val snapshot = query.get().await()
        snapshot.toObjects(Command::class.java)
    }

    /**
     * Get recent commands
     */
    suspend fun getRecentCommands(limit: Int = 20): Result<List<Command>> = safeCall {
        val query = commandCollection
            .orderBy("created", Query.Direction.DESCENDING)
            .limit(limit.toLong())

        val snapshot = query.get().await()
        snapshot.toObjects(Command::class.java)
    }

    /**
     * Get commands flow for real-time updates
     */
    fun getCommandsFlow(): Flow<List<Command>> = flow {
        val query = commandCollection.orderBy("created", Query.Direction.DESCENDING)
        
        query.addSnapshotListener { snapshot, error ->
            if (error == null && snapshot != null) {
                val commands = snapshot.toObjects(Command::class.java)
                // Note: In a real implementation, you'd use callbackFlow for proper flow emission
            }
        }
    }

    /**
     * Update command status
     */
    suspend fun updateCommandStatus(commandId: String, status: CommandStatus): Result<Command> = safeCall {
        validateRequired("commandId" to commandId)
        
        val command = getCommandById(commandId).getOrThrow()
        val updatedCommand = command.updateStatus(status)
        
        commandCollection.document(commandId).set(updatedCommand).await()
        updatedCommand
    }

    /**
     * Add observation to command
     */
    suspend fun addObservation(commandId: String, observation: String): Result<Command> = safeCall {
        validateRequired("commandId" to commandId, "observation" to observation)
        
        val command = getCommandById(commandId).getOrThrow()
        val updatedCommand = command.addObservation(observation)
        
        commandCollection.document(commandId).set(updatedCommand).await()
        updatedCommand
    }

    /**
     * Update payment proof
     */
    suspend fun updatePaymentProof(commandId: String, imageUrl: String): Result<Command> = safeCall {
        validateRequired("commandId" to commandId, "imageUrl" to imageUrl)
        
        val command = getCommandById(commandId).getOrThrow()
        val updatedCommand = command.updatePaymentProof(imageUrl)
        
        commandCollection.document(commandId).set(updatedCommand).await()
        updatedCommand
    }

    /**
     * Get commands requiring payment proof
     */
    suspend fun getCommandsRequiringPaymentProof(): Result<List<Command>> = safeCall {
        val query = commandCollection
            .whereEqualTo("proofUploaded", false)
            .whereGreaterThanOrEqualTo("commandStepIndex", CommandStatus.READY.ordinal)
            .orderBy("commandStepIndex")
            .orderBy("created", Query.Direction.DESCENDING)

        val snapshot = query.get().await()
        snapshot.toObjects(Command::class.java)
    }

    /**
     * Generate unique command number
     */
    private fun generateCommandNumber(): String {
        val timestamp = System.currentTimeMillis()
        val random = (1000..9999).random()
        return "CMD-$timestamp-$random"
    }

    /**
     * Archive command
     */
    suspend fun archiveCommand(commandId: String): Result<Command> = safeCall {
        validateRequired("commandId" to commandId)
        
        val command = getCommandById(commandId).getOrThrow()
        val archivedCommand = command.copy(
            isArchived = true,
            updatedAt = getCurrentTimestamp()
        )
        
        commandCollection.document(commandId).set(archivedCommand).await()
        archivedCommand
    }

    /**
     * Get command statistics
     */
    suspend fun getCommandStatistics(): Result<Map<String, Int>> = safeCall {
        val allCommands = commandCollection.get().await().toObjects(Command::class.java)
        
        mapOf(
            "total" to allCommands.size,
            "completed" to allCommands.count { it.isCompleted() },
            "pending" to allCommands.count { !it.isCompleted() },
            "requiresPayment" to allCommands.count { it.requiresPaymentProof() }
        )
    }
}
