package com.tfkcolin.joceladmin.repository

import com.google.firebase.firestore.CollectionReference
import com.google.firebase.firestore.Query
import com.tfkcolin.joceladmin.data.models.Cargo
import com.tfkcolin.joceladmin.data.models.CargoStatus
import com.tfkcolin.joceladmin.repository.base.BaseRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.tasks.await
import javax.inject.Inject
import javax.inject.Named
import javax.inject.Singleton

/**
 * Repository for cargo operations
 * Handles CRUD operations for cargo containers and logistics management
 */
@Singleton
class CargoRepository @Inject constructor(
    @Named("CARGO_DB") private val cargoCollection: CollectionReference
) : BaseRepository() {

    /**
     * Create a new cargo
     */
    suspend fun createCargo(cargo: Cargo): Result<Cargo> = safeCall {
        validateRequired("cargo" to cargo)
        
        val cargoId = if (cargo.id.isBlank()) generateId() else cargo.id
        val containerNumber = generateContainerNumber()
        
        val newCargo = cargo.copy(
            id = cargoId,
            containerNumber = containerNumber,
            created = getCurrentTimestamp(),
            updatedAt = getCurrentTimestamp(),
            searchIndex = generateSearchIndex(cargo.origin, cargo.destination)
        )

        cargoCollection.document(cargoId).set(newCargo).await()
        newCargo
    }

    /**
     * Get cargo by ID
     */
    suspend fun getCargoById(cargoId: String): Result<Cargo> = safeCall {
        validateRequired("cargoId" to cargoId)
        
        val document = cargoCollection.document(cargoId).get().await()
        document.toObject(Cargo::class.java) ?: throw Exception("Cargo not found")
    }

    /**
     * Update existing cargo
     */
    suspend fun updateCargo(cargo: Cargo): Result<Cargo> = safeCall {
        validateRequired("cargo" to cargo, "cargoId" to cargo.id)
        
        val updatedCargo = cargo.copy(
            updatedAt = getCurrentTimestamp(),
            searchIndex = generateSearchIndex(cargo.origin, cargo.destination)
        )

        cargoCollection.document(cargo.id).set(updatedCargo).await()
        updatedCargo
    }

    /**
     * Delete cargo
     */
    suspend fun deleteCargo(cargoId: String): Result<Unit> = safeCall {
        validateRequired("cargoId" to cargoId)
        cargoCollection.document(cargoId).delete().await()
    }

    /**
     * Get cargos by status
     */
    suspend fun getCargosByStatus(status: CargoStatus, limit: Int = 50): Result<List<Cargo>> = safeCall {
        val query = cargoCollection
            .whereEqualTo("statusIndex", status.ordinal)
            .orderBy("created", Query.Direction.DESCENDING)
            .limit(limit.toLong())

        val snapshot = query.get().await()
        snapshot.toObjects(Cargo::class.java)
    }

    /**
     * Get cargos by origin
     */
    suspend fun getCargosByOrigin(origin: String, limit: Int = 50): Result<List<Cargo>> = safeCall {
        validateRequired("origin" to origin)
        
        val query = cargoCollection
            .whereEqualTo("origin", origin)
            .orderBy("created", Query.Direction.DESCENDING)
            .limit(limit.toLong())

        val snapshot = query.get().await()
        snapshot.toObjects(Cargo::class.java)
    }

    /**
     * Get cargos by destination
     */
    suspend fun getCargosByDestination(destination: String, limit: Int = 50): Result<List<Cargo>> = safeCall {
        validateRequired("destination" to destination)
        
        val query = cargoCollection
            .whereEqualTo("destination", destination)
            .orderBy("created", Query.Direction.DESCENDING)
            .limit(limit.toLong())

        val snapshot = query.get().await()
        snapshot.toObjects(Cargo::class.java)
    }

    /**
     * Search cargos by route
     */
    suspend fun searchCargosByRoute(searchTerm: String, limit: Int = 50): Result<List<Cargo>> = safeCall {
        validateRequired("searchTerm" to searchTerm)
        
        val query = cargoCollection
            .whereGreaterThanOrEqualTo("searchIndex", searchTerm.lowercase())
            .whereLessThanOrEqualTo("searchIndex", searchTerm.lowercase() + '\uf8ff')
            .orderBy("searchIndex")
            .limit(limit.toLong())

        val snapshot = query.get().await()
        snapshot.toObjects(Cargo::class.java)
    }

    /**
     * Get recent cargos
     */
    suspend fun getRecentCargos(limit: Int = 20): Result<List<Cargo>> = safeCall {
        val query = cargoCollection
            .orderBy("created", Query.Direction.DESCENDING)
            .limit(limit.toLong())

        val snapshot = query.get().await()
        snapshot.toObjects(Cargo::class.java)
    }

    /**
     * Get cargos flow for real-time updates
     */
    fun getCargosFlow(): Flow<List<Cargo>> = flow {
        val query = cargoCollection.orderBy("created", Query.Direction.DESCENDING)
        
        query.addSnapshotListener { snapshot, error ->
            if (error == null && snapshot != null) {
                val cargos = snapshot.toObjects(Cargo::class.java)
                // Note: In a real implementation, you'd use callbackFlow for proper flow emission
            }
        }
    }

    /**
     * Update cargo status
     */
    suspend fun updateCargoStatus(cargoId: String, status: CargoStatus): Result<Cargo> = safeCall {
        validateRequired("cargoId" to cargoId)
        
        val cargo = getCargoById(cargoId).getOrThrow()
        val updatedCargo = cargo.updateStatus(status)
        
        cargoCollection.document(cargoId).set(updatedCargo).await()
        updatedCargo
    }

    /**
     * Add shipment to cargo
     */
    suspend fun addShipmentToCargo(cargoId: String, shipmentId: String): Result<Cargo> = safeCall {
        validateRequired("cargoId" to cargoId, "shipmentId" to shipmentId)
        
        val cargo = getCargoById(cargoId).getOrThrow()
        
        if (!cargo.canAcceptShipments()) {
            throw Exception("Cargo cannot accept new shipments")
        }
        
        val updatedShipmentIds = cargo.shipmentIds + shipmentId
        val updatedCargo = cargo.copy(
            shipmentIds = updatedShipmentIds,
            updatedAt = getCurrentTimestamp()
        )
        
        cargoCollection.document(cargoId).set(updatedCargo).await()
        updatedCargo
    }

    /**
     * Remove shipment from cargo
     */
    suspend fun removeShipmentFromCargo(cargoId: String, shipmentId: String): Result<Cargo> = safeCall {
        validateRequired("cargoId" to cargoId, "shipmentId" to shipmentId)
        
        val cargo = getCargoById(cargoId).getOrThrow()
        val updatedShipmentIds = cargo.shipmentIds - shipmentId
        val updatedCargo = cargo.copy(
            shipmentIds = updatedShipmentIds,
            updatedAt = getCurrentTimestamp()
        )
        
        cargoCollection.document(cargoId).set(updatedCargo).await()
        updatedCargo
    }

    /**
     * Get cargos that can accept shipments
     */
    suspend fun getAvailableCargos(): Result<List<Cargo>> = safeCall {
        val query = cargoCollection
            .whereEqualTo("statusIndex", CargoStatus.LOADING.ordinal)
            .orderBy("created", Query.Direction.DESCENDING)

        val snapshot = query.get().await()
        val cargos = snapshot.toObjects(Cargo::class.java)
        cargos.filter { it.canAcceptShipments() }
    }

    /**
     * Update cargo weight and volume
     */
    suspend fun updateCargoCapacity(
        cargoId: String, 
        totalWeight: Double, 
        totalVolume: Double
    ): Result<Cargo> = safeCall {
        validateRequired("cargoId" to cargoId)
        validatePositive(totalWeight, "totalWeight")
        validatePositive(totalVolume, "totalVolume")
        
        val cargo = getCargoById(cargoId).getOrThrow()
        val updatedCargo = cargo.copy(
            totalWeightKg = totalWeight,
            totalVolumeCbm = totalVolume,
            updatedAt = getCurrentTimestamp()
        )
        
        cargoCollection.document(cargoId).set(updatedCargo).await()
        updatedCargo
    }

    /**
     * Get cargo statistics
     */
    suspend fun getCargoStatistics(): Result<Map<String, Int>> = safeCall {
        val allCargos = cargoCollection.get().await().toObjects(Cargo::class.java)
        
        mapOf(
            "total" to allCargos.size,
            "loading" to allCargos.count { it.getStatus() == CargoStatus.LOADING },
            "inTransit" to allCargos.count { it.getStatus() == CargoStatus.IN_TRANSIT },
            "arrived" to allCargos.count { it.getStatus() == CargoStatus.ARRIVED },
            "available" to allCargos.count { it.canAcceptShipments() }
        )
    }

    /**
     * Generate unique container number
     */
    private fun generateContainerNumber(): String {
        val timestamp = System.currentTimeMillis()
        val random = (1000..9999).random()
        return "CONT-$timestamp-$random"
    }

    /**
     * Generate search index for better search functionality
     */
    private fun generateSearchIndex(origin: String, destination: String): String {
        return "$origin $destination ${origin.lowercase()} ${destination.lowercase()}".lowercase()
    }
}
