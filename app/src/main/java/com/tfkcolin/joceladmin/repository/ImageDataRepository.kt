package com.tfkcolin.joceladmin.repository

import androidx.paging.Pager
import androidx.paging.PagingConfig
import androidx.paging.PagingData
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.Query
import com.google.firebase.storage.FirebaseStorage
import com.tfkcolin.joceladmin.data.models.*
import com.tfkcolin.joceladmin.repository.base.BaseRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.tasks.await
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Repository for managing image data operations
 */
@Singleton
class ImageDataRepository @Inject constructor(
    private val firestore: FirebaseFirestore,
    private val storage: FirebaseStorage
) : BaseRepository() {

    companion object {
        private const val COLLECTION_IMAGES = "images"
        private const val STORAGE_PATH_IMAGES = "images"
        private const val STORAGE_PATH_THUMBNAILS = "thumbnails"
        private const val STORAGE_PATH_COMPRESSED = "compressed"
        private const val PAGE_SIZE = 20
    }

    /**
     * Get paginated images with optional filters
     */
    fun getImages(filters: ImageSearchFilters = ImageSearchFilters()): Flow<PagingData<ImageData>> {
        return Pager(
            config = PagingConfig(
                pageSize = PAGE_SIZE,
                enablePlaceholders = false
            ),
            pagingSourceFactory = {
                ImageDataPagingSource(
                    firestore = firestore,
                    filters = filters
                )
            }
        ).flow
    }

    /**
     * Get image by ID
     */
    suspend fun getImageById(id: String): Result<ImageData?> = safeCall {
        validateRequired("id" to id)
        
        val document = firestore.collection(COLLECTION_IMAGES)
            .document(id)
            .get()
            .await()
        
        document.toObject(ImageData::class.java)
    }

    /**
     * Get images by IDs
     */
    suspend fun getImagesByIds(ids: List<String>): Result<List<ImageData>> = safeCall {
        if (ids.isEmpty()) return@safeCall emptyList()
        
        val chunks = ids.chunked(10) // Firestore 'in' query limit
        val allImages = mutableListOf<ImageData>()
        
        chunks.forEach { chunk ->
            val documents = firestore.collection(COLLECTION_IMAGES)
                .whereIn("id", chunk)
                .get()
                .await()
            
            documents.documents.mapNotNull { 
                it.toObject(ImageData::class.java) 
            }.let { allImages.addAll(it) }
        }
        
        allImages
    }

    /**
     * Create new image data entry
     */
    suspend fun createImage(request: ImageUploadRequest): Result<ImageData> = safeCall {
        validateRequired(
            "filename" to request.filename,
            "category" to request.category
        )
        
        val id = generateId()
        val timestamp = getCurrentTimestamp()
        
        val imageData = ImageData(
            id = id,
            filename = request.filename,
            category = request.category,
            genre = request.genre,
            tags = request.tags,
            description = request.description,
            altText = request.altText,
            uploadedAt = timestamp,
            isActive = true,
            productInfo = request.productInfo
        )
        
        firestore.collection(COLLECTION_IMAGES)
            .document(id)
            .set(imageData)
            .await()
        
        imageData
    }

    /**
     * Update image data
     */
    suspend fun updateImage(id: String, updates: Map<String, Any>): Result<ImageData> = safeCall {
        validateRequired("id" to id)
        
        val updatedData = updates.toMutableMap()
        updatedData["uploadedAt"] = getCurrentTimestamp()
        
        firestore.collection(COLLECTION_IMAGES)
            .document(id)
            .update(updatedData)
            .await()
        
        // Return updated image
        getImageById(id).getOrThrow()!!
    }

    /**
     * Update image URLs after processing
     */
    suspend fun updateImageUrls(
        id: String,
        originalUrl: String,
        thumbnailUrl: String? = null,
        compressedUrl: String? = null,
        metadata: ImageMetadata
    ): Result<ImageData> = safeCall {
        validateRequired("id" to id, "originalUrl" to originalUrl)
        
        val updates = mutableMapOf<String, Any>(
            "originalUrl" to originalUrl,
            "metadata" to metadata,
            "uploadedAt" to getCurrentTimestamp()
        )
        
        thumbnailUrl?.let { updates["thumbnailUrl"] = it }
        compressedUrl?.let { updates["compressedUrl"] = it }
        
        firestore.collection(COLLECTION_IMAGES)
            .document(id)
            .update(updates)
            .await()
        
        getImageById(id).getOrThrow()!!
    }

    /**
     * Delete image data and files
     */
    suspend fun deleteImage(id: String): Result<Boolean> = safeCall {
        validateRequired("id" to id)
        
        // Get image data first to get file URLs
        val imageData = getImageById(id).getOrThrow()
            ?: throw IllegalArgumentException("Image not found")
        
        // Delete files from storage
        deleteImageFiles(imageData)
        
        // Delete document from Firestore
        firestore.collection(COLLECTION_IMAGES)
            .document(id)
            .delete()
            .await()
        
        true
    }

    /**
     * Delete multiple images
     */
    suspend fun deleteImages(ids: List<String>): Result<Int> = safeCall {
        var deletedCount = 0
        
        ids.forEach { id ->
            try {
                deleteImage(id).getOrThrow()
                deletedCount++
            } catch (e: Exception) {
                // Log error but continue with other deletions
                println("Failed to delete image $id: ${e.message}")
            }
        }
        
        deletedCount
    }

    /**
     * Get images by category
     */
    suspend fun getImagesByCategory(category: String, limit: Int = 50): Result<List<ImageData>> = safeCall {
        validateRequired("category" to category)
        
        val documents = firestore.collection(COLLECTION_IMAGES)
            .whereEqualTo("category", category)
            .whereEqualTo("isActive", true)
            .orderBy("uploadedAt", Query.Direction.DESCENDING)
            .limit(limit.toLong())
            .get()
            .await()
        
        documents.toObjects(ImageData::class.java)
    }

    /**
     * Get images by genre
     */
    suspend fun getImagesByGenre(genre: String, limit: Int = 50): Result<List<ImageData>> = safeCall {
        validateRequired("genre" to genre)
        
        val documents = firestore.collection(COLLECTION_IMAGES)
            .whereEqualTo("genre", genre)
            .whereEqualTo("isActive", true)
            .orderBy("uploadedAt", Query.Direction.DESCENDING)
            .limit(limit.toLong())
            .get()
            .await()
        
        documents.toObjects(ImageData::class.java)
    }

    /**
     * Search images by text
     */
    suspend fun searchImages(query: String, limit: Int = 50): Result<List<ImageData>> = safeCall {
        validateRequired("query" to query)
        
        // Note: This is a basic implementation. For production, consider using
        // Algolia or Elasticsearch for better full-text search capabilities
        val documents = firestore.collection(COLLECTION_IMAGES)
            .whereEqualTo("isActive", true)
            .orderBy("uploadedAt", Query.Direction.DESCENDING)
            .limit(limit.toLong())
            .get()
            .await()
        
        val allImages = documents.toObjects(ImageData::class.java)
        
        // Filter by query in filename, description, tags, or alt text
        allImages.filter { image ->
            query.lowercase() in image.filename.lowercase() ||
            query.lowercase() in image.description.lowercase() ||
            query.lowercase() in image.altText.lowercase() ||
            image.tags.any { tag -> query.lowercase() in tag.lowercase() }
        }
    }

    /**
     * Get image statistics
     */
    suspend fun getImageStats(): Result<ImageStats> = safeCall {
        val documents = firestore.collection(COLLECTION_IMAGES)
            .whereEqualTo("isActive", true)
            .get()
            .await()
        
        val images = documents.toObjects(ImageData::class.java)
        
        val categoryCount = images.groupingBy { it.category }.eachCount()
        val genreCount = images.groupingBy { it.genre }.eachCount()
        val formatCount = images.groupingBy { it.format }.eachCount()
        val totalStorage = images.sumOf { it.fileSize }
        val averageSize = if (images.isNotEmpty()) totalStorage / images.size else 0L
        
        // Calculate tag statistics
        val allTags = images.flatMap { it.tags }
        val tagCounts = allTags.groupingBy { it }.eachCount()
        val totalTags = allTags.size
        val topTags = tagCounts.entries
            .sortedByDescending { it.value }
            .take(10)
            .map { (tag, count) ->
                TagSummary(
                    tag = tag,
                    count = count,
                    percentage = if (totalTags > 0) (count.toDouble() / totalTags) * 100 else 0.0
                )
            }
        
        ImageStats(
            totalImages = images.size,
            imagesByCategory = categoryCount,
            imagesByGenre = genreCount,
            totalStorageUsed = totalStorage,
            averageFileSize = averageSize,
            mostPopularTags = topTags,
            formatDistribution = formatCount
        )
    }

    /**
     * Update download count
     */
    suspend fun incrementDownloadCount(id: String): Result<Boolean> = safeCall {
        validateRequired("id" to id)
        
        firestore.collection(COLLECTION_IMAGES)
            .document(id)
            .update("downloadCount", com.google.firebase.firestore.FieldValue.increment(1))
            .await()
        
        true
    }

    /**
     * Delete image files from storage
     */
    private suspend fun deleteImageFiles(imageData: ImageData) {
        try {
            // Delete original image
            if (imageData.originalUrl.isNotEmpty()) {
                storage.getReferenceFromUrl(imageData.originalUrl).delete().await()
            }
            
            // Delete thumbnail
            if (imageData.thumbnailUrl.isNotEmpty()) {
                storage.getReferenceFromUrl(imageData.thumbnailUrl).delete().await()
            }
            
            // Delete compressed version
            if (imageData.compressedUrl.isNotEmpty()) {
                storage.getReferenceFromUrl(imageData.compressedUrl).delete().await()
            }
        } catch (e: Exception) {
            // Log error but don't fail the operation
            println("Failed to delete some image files: ${e.message}")
        }
    }
}
