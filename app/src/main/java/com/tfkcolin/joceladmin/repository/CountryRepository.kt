package com.tfkcolin.joceladmin.repository

import com.google.firebase.firestore.CollectionReference
import com.google.firebase.firestore.Query
import com.tfkcolin.joceladmin.data.models.CountryData
import com.tfkcolin.joceladmin.repository.base.BaseRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.tasks.await
import javax.inject.Inject
import javax.inject.Named
import javax.inject.Singleton

/**
 * Repository for country configuration operations
 * Handles CRUD operations for multi-country setup and management
 */
@Singleton
class CountryRepository @Inject constructor(
    @Named("COUNTRY_DB") private val countryCollection: CollectionReference
) : BaseRepository() {

    /**
     * Create a new country configuration
     */
    suspend fun createCountry(country: CountryData): Result<CountryData> = safeCall {
        validateRequired("country" to country)
        
        val countryId = if (country.id.isBlank()) generateId() else country.id
        
        val newCountry = country.copy(
            id = countryId,
            created = getCurrentTimestamp(),
            updatedAt = getCurrentTimestamp()
        )

        countryCollection.document(countryId).set(newCountry).await()
        newCountry
    }

    /**
     * Get country by ID
     */
    suspend fun getCountryById(countryId: String): Result<CountryData> = safeCall {
        validateRequired("countryId" to countryId)
        
        val document = countryCollection.document(countryId).get().await()
        document.toObject(CountryData::class.java) ?: throw Exception("Country not found")
    }

    /**
     * Get country by name
     */
    suspend fun getCountryByName(name: String): Result<CountryData?> = safeCall {
        validateRequired("name" to name)
        
        val query = countryCollection
            .whereEqualTo("name", name)
            .limit(1)

        val snapshot = query.get().await()
        snapshot.toObjects(CountryData::class.java).firstOrNull()
    }

    /**
     * Update existing country
     */
    suspend fun updateCountry(country: CountryData): Result<CountryData> = safeCall {
        validateRequired("country" to country, "countryId" to country.id)
        
        val updatedCountry = country.copy(
            updatedAt = getCurrentTimestamp()
        )

        countryCollection.document(country.id).set(updatedCountry).await()
        updatedCountry
    }

    /**
     * Delete country
     */
    suspend fun deleteCountry(countryId: String): Result<Unit> = safeCall {
        validateRequired("countryId" to countryId)
        countryCollection.document(countryId).delete().await()
    }

    /**
     * Get all countries
     */
    suspend fun getAllCountries(): Result<List<CountryData>> = safeCall {
        val query = countryCollection.orderBy("name", Query.Direction.ASCENDING)
        val snapshot = query.get().await()
        snapshot.toObjects(CountryData::class.java)
    }

    /**
     * Get active countries
     */
    suspend fun getActiveCountries(): Result<List<CountryData>> = safeCall {
        val query = countryCollection
            .whereEqualTo("isActive", true)
            .orderBy("name", Query.Direction.ASCENDING)

        val snapshot = query.get().await()
        snapshot.toObjects(CountryData::class.java)
    }

    /**
     * Get countries by currency
     */
    suspend fun getCountriesByCurrency(currency: String): Result<List<CountryData>> = safeCall {
        validateRequired("currency" to currency)
        
        val query = countryCollection
            .whereEqualTo("devise", currency)
            .orderBy("name", Query.Direction.ASCENDING)

        val snapshot = query.get().await()
        snapshot.toObjects(CountryData::class.java)
    }

    /**
     * Get countries flow for real-time updates
     */
    fun getCountriesFlow(): Flow<List<CountryData>> = flow {
        val query = countryCollection.orderBy("name", Query.Direction.ASCENDING)
        
        query.addSnapshotListener { snapshot, error ->
            if (error == null && snapshot != null) {
                val countries = snapshot.toObjects(CountryData::class.java)
                // Note: In a real implementation, you'd use callbackFlow for proper flow emission
            }
        }
    }

    /**
     * Activate/deactivate country
     */
    suspend fun setCountryActive(countryId: String, isActive: Boolean): Result<CountryData> = safeCall {
        validateRequired("countryId" to countryId)
        
        val country = getCountryById(countryId).getOrThrow()
        val updatedCountry = country.copy(
            isActive = isActive,
            updatedAt = getCurrentTimestamp()
        )
        
        countryCollection.document(countryId).set(updatedCountry).await()
        updatedCountry
    }

    /**
     * Update country tax rate
     */
    suspend fun updateCountryTaxRate(countryId: String, taxRate: Double): Result<CountryData> = safeCall {
        validateRequired("countryId" to countryId)
        validatePositive(taxRate, "taxRate")
        
        val country = getCountryById(countryId).getOrThrow()
        val updatedCountry = country.copy(
            taxRate = taxRate,
            updatedAt = getCurrentTimestamp()
        )
        
        countryCollection.document(countryId).set(updatedCountry).await()
        updatedCountry
    }

    /**
     * Search countries by name
     */
    suspend fun searchCountriesByName(searchTerm: String): Result<List<CountryData>> = safeCall {
        validateRequired("searchTerm" to searchTerm)
        
        val query = countryCollection
            .whereGreaterThanOrEqualTo("name", searchTerm)
            .whereLessThanOrEqualTo("name", searchTerm + '\uf8ff')
            .orderBy("name")

        val snapshot = query.get().await()
        snapshot.toObjects(CountryData::class.java)
    }

    /**
     * Get country statistics
     */
    suspend fun getCountryStatistics(): Result<Map<String, Int>> = safeCall {
        val allCountries = getAllCountries().getOrThrow()
        
        mapOf(
            "total" to allCountries.size,
            "active" to allCountries.count { it.isActive },
            "inactive" to allCountries.count { !it.isActive },
            "uniqueCurrencies" to allCountries.map { it.devise }.distinct().size
        )
    }

    /**
     * Check if country name exists
     */
    suspend fun isCountryNameExists(name: String, excludeId: String? = null): Result<Boolean> = safeCall {
        validateRequired("name" to name)
        
        val existingCountry = getCountryByName(name).getOrNull()
        
        if (existingCountry == null) {
            false
        } else {
            excludeId == null || existingCountry.id != excludeId
        }
    }

    /**
     * Get supported currencies
     */
    suspend fun getSupportedCurrencies(): Result<List<String>> = safeCall {
        val countries = getAllCountries().getOrThrow()
        countries.map { it.devise }.distinct().sorted()
    }

    /**
     * Initialize default countries
     */
    suspend fun initializeDefaultCountries(): Result<List<CountryData>> = safeCall {
        val defaultCountries = listOf(
            CountryData(
                name = "United States",
                devise = "USD",
                currencySymbol = "$",
                isActive = true,
                taxRate = 8.5
            ),
            CountryData(
                name = "Canada",
                devise = "CAD",
                currencySymbol = "C$",
                isActive = true,
                taxRate = 13.0
            ),
            CountryData(
                name = "France",
                devise = "EUR",
                currencySymbol = "€",
                isActive = true,
                taxRate = 20.0
            ),
            CountryData(
                name = "United Kingdom",
                devise = "GBP",
                currencySymbol = "£",
                isActive = true,
                taxRate = 20.0
            )
        )

        val createdCountries = mutableListOf<CountryData>()
        
        for (country in defaultCountries) {
            val exists = isCountryNameExists(country.name).getOrThrow()
            if (!exists) {
                val created = createCountry(country).getOrThrow()
                createdCountries.add(created)
            }
        }
        
        createdCountries
    }
}
