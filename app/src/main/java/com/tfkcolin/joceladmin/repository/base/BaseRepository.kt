package com.tfkcolin.joceladmin.repository.base

import com.google.firebase.firestore.FirebaseFirestoreException
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * Base repository class with common Firebase operations and error handling
 * Based on the architecture documentation's error handling patterns
 */
abstract class BaseRepository {

    /**
     * Exception class for repository operations
     */
    class RepositoryException(message: String?, cause: Throwable?) : Exception(message, cause)

    /**
     * Safe execution wrapper for Firebase operations
     */
    protected suspend fun <T> safeCall(
        onError: ((String?) -> Unit)? = null,
        operation: suspend () -> T
    ): Result<T> = try {
        withContext(Dispatchers.IO) {
            Result.success(operation())
        }
    } catch (e: Exception) {
        val errorMessage = handleFirebaseException(e)
        onError?.invoke(errorMessage)
        Result.failure(RepositoryException(errorMessage, e.cause))
    }

    /**
     * Handle Firebase-specific exceptions
     */
    private fun handleFirebaseException(exception: Exception): String {
        return when (exception) {
            is FirebaseFirestoreException -> {
                when (exception.code) {
                    FirebaseFirestoreException.Code.PERMISSION_DENIED -> 
                        "Permission denied. Please check your access rights."
                    FirebaseFirestoreException.Code.NOT_FOUND -> 
                        "Requested data not found."
                    FirebaseFirestoreException.Code.ALREADY_EXISTS -> 
                        "Data already exists."
                    FirebaseFirestoreException.Code.RESOURCE_EXHAUSTED -> 
                        "Resource limit exceeded. Please try again later."
                    FirebaseFirestoreException.Code.FAILED_PRECONDITION -> 
                        "Operation failed due to precondition."
                    FirebaseFirestoreException.Code.ABORTED -> 
                        "Operation was aborted. Please try again."
                    FirebaseFirestoreException.Code.OUT_OF_RANGE -> 
                        "Operation out of valid range."
                    FirebaseFirestoreException.Code.UNIMPLEMENTED -> 
                        "Operation not implemented."
                    FirebaseFirestoreException.Code.INTERNAL -> 
                        "Internal server error. Please try again later."
                    FirebaseFirestoreException.Code.UNAVAILABLE -> 
                        "Service temporarily unavailable. Please try again later."
                    FirebaseFirestoreException.Code.DATA_LOSS -> 
                        "Data loss detected. Please contact support."
                    FirebaseFirestoreException.Code.UNAUTHENTICATED -> 
                        "Authentication required. Please sign in."
                    FirebaseFirestoreException.Code.INVALID_ARGUMENT -> 
                        "Invalid argument provided."
                    FirebaseFirestoreException.Code.DEADLINE_EXCEEDED -> 
                        "Operation timed out. Please try again."
                    FirebaseFirestoreException.Code.CANCELLED -> 
                        "Operation was cancelled."
                    else -> "An unexpected error occurred: ${exception.message}"
                }
            }
            else -> exception.message ?: "An unknown error occurred"
        }
    }

    /**
     * Generate a unique ID for new documents
     */
    protected fun generateId(): String {
        return System.currentTimeMillis().toString() + (1000..9999).random()
    }

    /**
     * Get current timestamp
     */
    protected fun getCurrentTimestamp(): Long {
        return System.currentTimeMillis()
    }

    /**
     * Validate required fields
     */
    protected fun validateRequired(vararg fields: Pair<String, Any?>) {
        fields.forEach { (fieldName, value) ->
            when (value) {
                null -> throw IllegalArgumentException("$fieldName is required")
                is String -> if (value.isBlank()) throw IllegalArgumentException("$fieldName cannot be empty")
                is Collection<*> -> if (value.isEmpty()) throw IllegalArgumentException("$fieldName cannot be empty")
            }
        }
    }

    /**
     * Validate email format
     */
    protected fun validateEmail(email: String) {
        val emailRegex = "^[A-Za-z0-9+_.-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$"
        if (!email.matches(emailRegex.toRegex())) {
            throw IllegalArgumentException("Invalid email format")
        }
    }

    /**
     * Validate positive number
     */
    protected fun validatePositive(value: Double, fieldName: String) {
        if (value < 0) {
            throw IllegalArgumentException("$fieldName must be positive")
        }
    }

    /**
     * Validate positive integer
     */
    protected fun validatePositive(value: Int, fieldName: String) {
        if (value < 0) {
            throw IllegalArgumentException("$fieldName must be positive")
        }
    }
}
