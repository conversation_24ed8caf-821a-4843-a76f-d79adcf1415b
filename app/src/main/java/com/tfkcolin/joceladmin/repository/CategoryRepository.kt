package com.tfkcolin.joceladmin.repository

import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.Query
import com.tfkcolin.joceladmin.data.models.*
import com.tfkcolin.joceladmin.repository.base.BaseRepository
import kotlinx.coroutines.tasks.await
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Repository for managing category and genre operations
 */
@Singleton
class CategoryRepository @Inject constructor(
    private val firestore: FirebaseFirestore
) : BaseRepository() {

    companion object {
        private const val COLLECTION_CATEGORIES = "categories"
        private const val COLLECTION_GENRES = "genres"
    }

    // ========== Category Operations ==========

    /**
     * Get all categories
     */
    suspend fun getCategories(filters: CategorySearchFilters = CategorySearchFilters()): Result<List<Category>> = safeCall {
        var query: Query = firestore.collection(COLLECTION_CATEGORIES)
        
        // Apply filters
        filters.parentId?.let { parentId ->
            query = query.whereEqualTo("parentId", parentId)
        }
        
        filters.level?.let { level ->
            query = query.whereEqualTo("level", level)
        }
        
        filters.isActive?.let { isActive ->
            query = query.whereEqualTo("isActive", isActive)
        }
        
        filters.isVisible?.let { isVisible ->
            query = query.whereEqualTo("isVisible", isVisible)
        }
        
        // Apply sorting
        val sortField = when (filters.sortBy) {
            CategorySortBy.NAME -> "name"
            CategorySortBy.SORT_ORDER -> "sortOrder"
            CategorySortBy.PRODUCT_COUNT -> "productCount"
            CategorySortBy.IMAGE_COUNT -> "imageCount"
            CategorySortBy.CREATED_DATE -> "createdAt"
            CategorySortBy.UPDATED_DATE -> "updatedAt"
        }
        
        val sortDirection = when (filters.sortOrder) {
            SortOrder.ASC -> Query.Direction.ASCENDING
            SortOrder.DESC -> Query.Direction.DESCENDING
        }
        
        query = query.orderBy(sortField, sortDirection)
        
        val documents = query.get().await()
        val categories = documents.toObjects(Category::class.java)
        
        // Filter by search query if provided
        if (filters.searchQuery.isNotEmpty()) {
            categories.filter { category ->
                filters.searchQuery.lowercase() in category.name.lowercase() ||
                filters.searchQuery.lowercase() in category.description.lowercase()
            }
        } else {
            categories
        }
    }

    /**
     * Get category by ID
     */
    suspend fun getCategoryById(id: String): Result<Category?> = safeCall {
        validateRequired("id" to id)
        
        val document = firestore.collection(COLLECTION_CATEGORIES)
            .document(id)
            .get()
            .await()
        
        document.toObject(Category::class.java)
    }

    /**
     * Get root categories (level 0)
     */
    suspend fun getRootCategories(): Result<List<Category>> = safeCall {
        val documents = firestore.collection(COLLECTION_CATEGORIES)
            .whereEqualTo("level", 0)
            .whereEqualTo("isActive", true)
            .whereEqualTo("isVisible", true)
            .orderBy("sortOrder")
            .get()
            .await()
        
        documents.toObjects(Category::class.java)
    }

    /**
     * Get subcategories of a parent category
     */
    suspend fun getSubcategories(parentId: String): Result<List<Category>> = safeCall {
        validateRequired("parentId" to parentId)
        
        val documents = firestore.collection(COLLECTION_CATEGORIES)
            .whereEqualTo("parentId", parentId)
            .whereEqualTo("isActive", true)
            .whereEqualTo("isVisible", true)
            .orderBy("sortOrder")
            .get()
            .await()
        
        documents.toObjects(Category::class.java)
    }

    /**
     * Get category hierarchy as tree structure
     */
    suspend fun getCategoryTree(): Result<List<CategoryTreeNode>> = safeCall {
        val allCategories = getCategories().getOrThrow()
        buildCategoryTree(allCategories)
    }

    /**
     * Create new category
     */
    suspend fun createCategory(request: CategoryCreateRequest): Result<Category> = safeCall {
        validateRequired("name" to request.name)
        
        val id = generateId()
        val timestamp = getCurrentTimestamp()
        
        // Calculate level and path
        val (level, path) = if (request.parentId != null) {
            val parent = getCategoryById(request.parentId).getOrThrow()
                ?: throw IllegalArgumentException("Parent category not found")
            Pair(parent.level + 1, "${parent.path}/${request.slug.ifEmpty { generateSlug(request.name) }}")
        } else {
            Pair(0, request.slug.ifEmpty { generateSlug(request.name) })
        }
        
        val category = Category(
            id = id,
            name = request.name,
            description = request.description,
            slug = request.slug.ifEmpty { generateSlug(request.name) },
            parentId = request.parentId,
            level = level,
            path = path,
            iconUrl = request.iconUrl,
            bannerUrl = request.bannerUrl,
            sortOrder = request.sortOrder,
            seoData = request.seoData,
            metadata = request.metadata,
            createdAt = timestamp,
            updatedAt = timestamp,
            isActive = true,
            isVisible = true
        )
        
        firestore.collection(COLLECTION_CATEGORIES)
            .document(id)
            .set(category)
            .await()
        
        category
    }

    /**
     * Update category
     */
    suspend fun updateCategory(request: CategoryUpdateRequest): Result<Category> = safeCall {
        validateRequired("id" to request.id)
        
        val updates = mutableMapOf<String, Any>()
        
        request.name?.let { updates["name"] = it }
        request.description?.let { updates["description"] = it }
        request.slug?.let { updates["slug"] = it }
        request.parentId?.let { updates["parentId"] = it }
        request.iconUrl?.let { updates["iconUrl"] = it }
        request.bannerUrl?.let { updates["bannerUrl"] = it }
        request.isActive?.let { updates["isActive"] = it }
        request.isVisible?.let { updates["isVisible"] = it }
        request.sortOrder?.let { updates["sortOrder"] = it }
        request.seoData?.let { updates["seoData"] = it }
        request.metadata?.let { updates["metadata"] = it }
        
        updates["updatedAt"] = getCurrentTimestamp()
        
        firestore.collection(COLLECTION_CATEGORIES)
            .document(request.id)
            .update(updates)
            .await()
        
        getCategoryById(request.id).getOrThrow()!!
    }

    /**
     * Delete category
     */
    suspend fun deleteCategory(id: String): Result<Boolean> = safeCall {
        validateRequired("id" to id)
        
        // Check if category has subcategories
        val subcategories = getSubcategories(id).getOrThrow()
        if (subcategories.isNotEmpty()) {
            throw IllegalStateException("Cannot delete category with subcategories")
        }
        
        // Delete associated genres first
        val genres = getGenresByCategory(id).getOrThrow()
        genres.forEach { genre ->
            deleteGenre(genre.id).getOrThrow()
        }
        
        firestore.collection(COLLECTION_CATEGORIES)
            .document(id)
            .delete()
            .await()
        
        true
    }

    // ========== Genre Operations ==========

    /**
     * Get all genres
     */
    suspend fun getGenres(): Result<List<Genre>> = safeCall {
        val documents = firestore.collection(COLLECTION_GENRES)
            .whereEqualTo("isActive", true)
            .orderBy("categoryId")
            .orderBy("sortOrder")
            .get()
            .await()
        
        documents.toObjects(Genre::class.java)
    }

    /**
     * Get genre by ID
     */
    suspend fun getGenreById(id: String): Result<Genre?> = safeCall {
        validateRequired("id" to id)
        
        val document = firestore.collection(COLLECTION_GENRES)
            .document(id)
            .get()
            .await()
        
        document.toObject(Genre::class.java)
    }

    /**
     * Get genres by category
     */
    suspend fun getGenresByCategory(categoryId: String): Result<List<Genre>> = safeCall {
        validateRequired("categoryId" to categoryId)
        
        val documents = firestore.collection(COLLECTION_GENRES)
            .whereEqualTo("categoryId", categoryId)
            .whereEqualTo("isActive", true)
            .whereEqualTo("isVisible", true)
            .orderBy("sortOrder")
            .get()
            .await()
        
        documents.toObjects(Genre::class.java)
    }

    /**
     * Create new genre
     */
    suspend fun createGenre(request: GenreCreateRequest): Result<Genre> = safeCall {
        validateRequired("name" to request.name, "categoryId" to request.categoryId)
        
        // Verify category exists
        getCategoryById(request.categoryId).getOrThrow()
            ?: throw IllegalArgumentException("Category not found")
        
        val id = generateId()
        val timestamp = getCurrentTimestamp()
        
        val genre = Genre(
            id = id,
            name = request.name,
            description = request.description,
            slug = request.slug.ifEmpty { generateSlug(request.name) },
            categoryId = request.categoryId,
            iconUrl = request.iconUrl,
            sortOrder = request.sortOrder,
            tags = request.tags,
            seoData = request.seoData,
            createdAt = timestamp,
            updatedAt = timestamp,
            isActive = true,
            isVisible = true
        )
        
        firestore.collection(COLLECTION_GENRES)
            .document(id)
            .set(genre)
            .await()
        
        genre
    }

    /**
     * Update genre
     */
    suspend fun updateGenre(request: GenreUpdateRequest): Result<Genre> = safeCall {
        validateRequired("id" to request.id)
        
        val updates = mutableMapOf<String, Any>()
        
        request.name?.let { updates["name"] = it }
        request.description?.let { updates["description"] = it }
        request.slug?.let { updates["slug"] = it }
        request.categoryId?.let { updates["categoryId"] = it }
        request.iconUrl?.let { updates["iconUrl"] = it }
        request.isActive?.let { updates["isActive"] = it }
        request.isVisible?.let { updates["isVisible"] = it }
        request.sortOrder?.let { updates["sortOrder"] = it }
        request.tags?.let { updates["tags"] = it }
        request.seoData?.let { updates["seoData"] = it }
        
        updates["updatedAt"] = getCurrentTimestamp()
        
        firestore.collection(COLLECTION_GENRES)
            .document(request.id)
            .update(updates)
            .await()
        
        getGenreById(request.id).getOrThrow()!!
    }

    /**
     * Delete genre
     */
    suspend fun deleteGenre(id: String): Result<Boolean> = safeCall {
        validateRequired("id" to id)
        
        firestore.collection(COLLECTION_GENRES)
            .document(id)
            .delete()
            .await()
        
        true
    }

    /**
     * Get category statistics
     */
    suspend fun getCategoryStats(): Result<CategoryStats> = safeCall {
        val categories = getCategories().getOrThrow()
        
        val totalCategories = categories.size
        val activeCategories = categories.count { it.isActive }
        val visibleCategories = categories.count { it.isVisible }
        val categoriesWithProducts = categories.count { it.productCount > 0 }
        val categoriesWithImages = categories.count { it.imageCount > 0 }
        
        val averageProductsPerCategory = if (categories.isNotEmpty()) {
            categories.map { it.productCount }.average()
        } else 0.0
        
        val averageImagesPerCategory = if (categories.isNotEmpty()) {
            categories.map { it.imageCount }.average()
        } else 0.0
        
        val topCategories = categories
            .sortedByDescending { it.productCount }
            .take(10)
            .map { category ->
                CategorySummary(
                    id = category.id,
                    name = category.name,
                    slug = category.slug,
                    level = category.level,
                    productCount = category.productCount,
                    imageCount = category.imageCount,
                    isActive = category.isActive,
                    isVisible = category.isVisible,
                    iconUrl = category.iconUrl
                )
            }
        
        val categoryTree = getCategoryTree().getOrThrow()
        
        CategoryStats(
            totalCategories = totalCategories,
            activeCategories = activeCategories,
            visibleCategories = visibleCategories,
            categoriesWithProducts = categoriesWithProducts,
            categoriesWithImages = categoriesWithImages,
            averageProductsPerCategory = averageProductsPerCategory,
            averageImagesPerCategory = averageImagesPerCategory,
            topCategories = topCategories,
            categoryHierarchy = categoryTree
        )
    }

    /**
     * Build category tree from flat list
     */
    private fun buildCategoryTree(categories: List<Category>): List<CategoryTreeNode> {
        val categoryMap = categories.associateBy { it.id }
        val rootNodes = mutableListOf<CategoryTreeNode>()
        
        categories.filter { it.parentId == null }.forEach { rootCategory ->
            val node = buildCategoryNode(rootCategory, categoryMap)
            rootNodes.add(node)
        }
        
        return rootNodes.sortedBy { it.category.sortOrder }
    }

    /**
     * Build category tree node recursively
     */
    private fun buildCategoryNode(category: Category, categoryMap: Map<String, Category>): CategoryTreeNode {
        val children = categoryMap.values
            .filter { it.parentId == category.id }
            .map { buildCategoryNode(it, categoryMap) }
            .sortedBy { it.category.sortOrder }
        
        return CategoryTreeNode(
            category = category,
            children = children,
            level = category.level
        )
    }

    /**
     * Generate URL-friendly slug from name
     */
    private fun generateSlug(name: String): String {
        return name.lowercase()
            .replace(Regex("[^a-z0-9\\s-]"), "")
            .replace(Regex("\\s+"), "-")
            .trim('-')
    }
}
