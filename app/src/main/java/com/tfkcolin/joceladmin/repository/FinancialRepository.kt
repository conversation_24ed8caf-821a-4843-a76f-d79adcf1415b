package com.tfkcolin.joceladmin.repository

import com.google.firebase.firestore.CollectionReference
import com.google.firebase.firestore.Query
import com.tfkcolin.joceladmin.data.models.FinancialTransaction
import com.tfkcolin.joceladmin.data.models.TransactionType
import com.tfkcolin.joceladmin.repository.base.BaseRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.tasks.await
import javax.inject.Inject
import javax.inject.Named
import javax.inject.Singleton

/**
 * Repository for financial transaction operations
 * Handles CRUD operations for multi-country financial management
 */
@Singleton
class FinancialRepository @Inject constructor(
    @Named("TRANSACTION_DB") private val transactionCollection: CollectionReference
) : BaseRepository() {

    /**
     * Create a new financial transaction
     */
    suspend fun createTransaction(transaction: FinancialTransaction): Result<FinancialTransaction> = safeCall {
        validateRequired("transaction" to transaction)
        
        val transactionId = if (transaction.id == "0") generateId() else transaction.id
        
        val newTransaction = transaction.copy(
            id = transactionId,
            created = getCurrentTimestamp(),
            updatedAt = getCurrentTimestamp()
        )

        transactionCollection.document(transactionId).set(newTransaction).await()
        newTransaction
    }

    /**
     * Get transaction by ID
     */
    suspend fun getTransactionById(transactionId: String): Result<FinancialTransaction> = safeCall {
        validateRequired("transactionId" to transactionId)
        
        val document = transactionCollection.document(transactionId).get().await()
        document.toObject(FinancialTransaction::class.java) ?: throw Exception("Transaction not found")
    }

    /**
     * Update existing transaction
     */
    suspend fun updateTransaction(transaction: FinancialTransaction): Result<FinancialTransaction> = safeCall {
        validateRequired("transaction" to transaction, "transactionId" to transaction.id)
        
        val updatedTransaction = transaction.copy(
            updatedAt = getCurrentTimestamp()
        )

        transactionCollection.document(transaction.id).set(updatedTransaction).await()
        updatedTransaction
    }

    /**
     * Delete transaction
     */
    suspend fun deleteTransaction(transactionId: String): Result<Unit> = safeCall {
        validateRequired("transactionId" to transactionId)
        transactionCollection.document(transactionId).delete().await()
    }

    /**
     * Get transactions by country
     */
    suspend fun getTransactionsByCountry(country: String, limit: Int = 50): Result<List<FinancialTransaction>> = safeCall {
        validateRequired("country" to country)
        
        val query = transactionCollection
            .whereEqualTo("country", country)
            .orderBy("created", Query.Direction.DESCENDING)
            .limit(limit.toLong())

        val snapshot = query.get().await()
        snapshot.toObjects(FinancialTransaction::class.java)
    }

    /**
     * Get transactions by type
     */
    suspend fun getTransactionsByType(
        type: TransactionType, 
        country: String? = null,
        limit: Int = 50
    ): Result<List<FinancialTransaction>> = safeCall {
        var query = transactionCollection
            .whereEqualTo("transactionTypeIndex", type.ordinal)
            .orderBy("created", Query.Direction.DESCENDING)
            .limit(limit.toLong())

        if (country != null) {
            query = transactionCollection
                .whereEqualTo("transactionTypeIndex", type.ordinal)
                .whereEqualTo("country", country)
                .orderBy("created", Query.Direction.DESCENDING)
                .limit(limit.toLong())
        }

        val snapshot = query.get().await()
        snapshot.toObjects(FinancialTransaction::class.java)
    }

    /**
     * Get transactions by command ID
     */
    suspend fun getTransactionsByCommandId(commandId: String): Result<List<FinancialTransaction>> = safeCall {
        validateRequired("commandId" to commandId)
        
        val query = transactionCollection
            .whereEqualTo("commandId", commandId)
            .orderBy("created", Query.Direction.DESCENDING)

        val snapshot = query.get().await()
        snapshot.toObjects(FinancialTransaction::class.java)
    }

    /**
     * Get transactions by date range
     */
    suspend fun getTransactionsByDateRange(
        startDate: Long,
        endDate: Long,
        country: String? = null,
        limit: Int = 100
    ): Result<List<FinancialTransaction>> = safeCall {
        var query = transactionCollection
            .whereGreaterThanOrEqualTo("created", startDate)
            .whereLessThanOrEqualTo("created", endDate)
            .orderBy("created", Query.Direction.DESCENDING)
            .limit(limit.toLong())

        if (country != null) {
            query = transactionCollection
                .whereEqualTo("country", country)
                .whereGreaterThanOrEqualTo("created", startDate)
                .whereLessThanOrEqualTo("created", endDate)
                .orderBy("created", Query.Direction.DESCENDING)
                .limit(limit.toLong())
        }

        val snapshot = query.get().await()
        snapshot.toObjects(FinancialTransaction::class.java)
    }

    /**
     * Get recent transactions
     */
    suspend fun getRecentTransactions(limit: Int = 20): Result<List<FinancialTransaction>> = safeCall {
        val query = transactionCollection
            .orderBy("created", Query.Direction.DESCENDING)
            .limit(limit.toLong())

        val snapshot = query.get().await()
        snapshot.toObjects(FinancialTransaction::class.java)
    }

    /**
     * Get transactions flow for real-time updates
     */
    fun getTransactionsFlow(): Flow<List<FinancialTransaction>> = flow {
        val query = transactionCollection.orderBy("created", Query.Direction.DESCENDING)
        
        query.addSnapshotListener { snapshot, error ->
            if (error == null && snapshot != null) {
                val transactions = snapshot.toObjects(FinancialTransaction::class.java)
                // Note: In a real implementation, you'd use callbackFlow for proper flow emission
            }
        }
    }

    /**
     * Mark transaction
     */
    suspend fun markTransaction(transactionId: String, marked: Boolean): Result<FinancialTransaction> = safeCall {
        validateRequired("transactionId" to transactionId)
        
        val transaction = getTransactionById(transactionId).getOrThrow()
        val updatedTransaction = transaction.copy(
            marked = marked,
            updatedAt = getCurrentTimestamp()
        )
        
        transactionCollection.document(transactionId).set(updatedTransaction).await()
        updatedTransaction
    }

    /**
     * Get financial summary by country
     */
    suspend fun getFinancialSummaryByCountry(country: String): Result<Map<String, Double>> = safeCall {
        validateRequired("country" to country)
        
        val transactions = getTransactionsByCountry(country, 1000).getOrThrow()
        
        val income = transactions.filter { it.isIncome() }.sumOf { it.price.toDouble() }
        val expenses = transactions.filter { it.isExpense() }.sumOf { it.price.toDouble() }
        val profit = income - expenses
        val profitMargin = if (income > 0) (profit / income) * 100 else 0.0
        
        mapOf(
            "income" to income,
            "expenses" to expenses,
            "profit" to profit,
            "profitMargin" to profitMargin,
            "transactionCount" to transactions.size.toDouble()
        )
    }

    /**
     * Get financial statistics
     */
    suspend fun getFinancialStatistics(): Result<Map<String, Any>> = safeCall {
        val allTransactions = transactionCollection.get().await().toObjects(FinancialTransaction::class.java)
        
        val totalIncome = allTransactions.filter { it.isIncome() }.sumOf { it.price.toDouble() }
        val totalExpenses = allTransactions.filter { it.isExpense() }.sumOf { it.price.toDouble() }
        val netProfit = totalIncome - totalExpenses
        
        val countryBreakdown = allTransactions.groupBy { it.country }
            .mapValues { (_, transactions) ->
                val income = transactions.filter { it.isIncome() }.sumOf { it.price.toDouble() }
                val expenses = transactions.filter { it.isExpense() }.sumOf { it.price.toDouble() }
                mapOf(
                    "income" to income,
                    "expenses" to expenses,
                    "profit" to (income - expenses)
                )
            }
        
        mapOf(
            "totalIncome" to totalIncome,
            "totalExpenses" to totalExpenses,
            "netProfit" to netProfit,
            "transactionCount" to allTransactions.size,
            "countryBreakdown" to countryBreakdown,
            "markedTransactions" to allTransactions.count { it.marked },
            "linkedToCommands" to allTransactions.count { it.isLinkedToCommand() }
        )
    }

    /**
     * Search transactions by label
     */
    suspend fun searchTransactionsByLabel(searchTerm: String, limit: Int = 50): Result<List<FinancialTransaction>> = safeCall {
        validateRequired("searchTerm" to searchTerm)
        
        val query = transactionCollection
            .whereGreaterThanOrEqualTo("label", searchTerm)
            .whereLessThanOrEqualTo("label", searchTerm + '\uf8ff')
            .orderBy("label")
            .limit(limit.toLong())

        val snapshot = query.get().await()
        snapshot.toObjects(FinancialTransaction::class.java)
    }

    /**
     * Get transactions by category
     */
    suspend fun getTransactionsByCategory(category: String, limit: Int = 50): Result<List<FinancialTransaction>> = safeCall {
        validateRequired("category" to category)
        
        val query = transactionCollection
            .whereEqualTo("category", category)
            .orderBy("created", Query.Direction.DESCENDING)
            .limit(limit.toLong())

        val snapshot = query.get().await()
        snapshot.toObjects(FinancialTransaction::class.java)
    }
}
