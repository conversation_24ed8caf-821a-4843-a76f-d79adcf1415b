package com.tfkcolin.joceladmin.utils

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Matrix
import android.media.ExifInterface
import android.net.Uri
import android.webkit.MimeTypeMap
import androidx.palette.graphics.Palette
import com.tfkcolin.joceladmin.data.models.ImageMetadata
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.ByteArrayOutputStream
import java.io.File
import java.io.FileOutputStream
import java.io.InputStream
import java.text.SimpleDateFormat
import java.util.*
import kotlin.math.max
import kotlin.math.min

/**
 * Utility class for image processing, compression, and metadata extraction
 */
object ImageUtils {

    private const val MAX_IMAGE_SIZE = 2048 // Maximum width/height for processed images
    private const val THUMBNAIL_SIZE = 300 // Thumbnail size
    private const val JPEG_QUALITY = 85 // JPEG compression quality
    private const val THUMBNAIL_QUALITY = 75 // Thumbnail compression quality

    /**
     * Process uploaded image: extract metadata, create thumbnail, compress
     */
    suspend fun processImage(
        context: Context,
        uri: Uri,
        filename: String
    ): Result<ProcessedImageData> = withContext(Dispatchers.IO) {
        try {
            val inputStream = context.contentResolver.openInputStream(uri)
                ?: return@withContext Result.failure(Exception("Cannot open input stream"))

            // Extract original metadata
            val originalBitmap = BitmapFactory.decodeStream(inputStream)
                ?: return@withContext Result.failure(Exception("Cannot decode image"))

            inputStream.close()

            // Extract EXIF data
            val exifData = extractExifData(context, uri)
            
            // Extract color palette
            val colorPalette = extractColorPalette(originalBitmap)
            
            // Create metadata
            val metadata = ImageMetadata(
                exifData = exifData,
                colorProfile = "sRGB", // Default assumption
                hasTransparency = hasTransparency(originalBitmap),
                dominantColors = colorPalette
            )

            // Correct orientation based on EXIF
            val correctedBitmap = correctImageOrientation(originalBitmap, exifData["Orientation"]?.toIntOrNull() ?: 1)

            // Create compressed version
            val compressedBitmap = compressImage(correctedBitmap, MAX_IMAGE_SIZE)
            val compressedFile = createTempFile(context, "compressed_$filename", "jpg")
            saveImageToFile(compressedBitmap, compressedFile, JPEG_QUALITY)

            // Create thumbnail
            val thumbnailBitmap = compressImage(correctedBitmap, THUMBNAIL_SIZE)
            val thumbnailFile = createTempFile(context, "thumb_$filename", "jpg")
            saveImageToFile(thumbnailBitmap, thumbnailFile, THUMBNAIL_QUALITY)

            // Save original (if different from compressed)
            val originalFile = if (needsCompression(originalBitmap)) {
                val file = createTempFile(context, "original_$filename", getFileExtension(filename))
                saveOriginalToFile(context, uri, file)
                file
            } else {
                compressedFile
            }

            // Clean up bitmaps
            originalBitmap.recycle()
            correctedBitmap.recycle()
            compressedBitmap.recycle()
            thumbnailBitmap.recycle()

            Result.success(
                ProcessedImageData(
                    originalFile = originalFile,
                    compressedFile = compressedFile,
                    thumbnailFile = thumbnailFile,
                    metadata = metadata,
                    width = originalBitmap.width,
                    height = originalBitmap.height,
                    fileSize = getFileSize(context, uri)
                )
            )
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Extract EXIF data from image
     */
    private suspend fun extractExifData(context: Context, uri: Uri): Map<String, String> = withContext(Dispatchers.IO) {
        try {
            val inputStream = context.contentResolver.openInputStream(uri)
            val exif = ExifInterface(inputStream!!)
            inputStream.close()

            mapOf(
                "DateTime" to (exif.getAttribute(ExifInterface.TAG_DATETIME) ?: ""),
                "Make" to (exif.getAttribute(ExifInterface.TAG_MAKE) ?: ""),
                "Model" to (exif.getAttribute(ExifInterface.TAG_MODEL) ?: ""),
                "Orientation" to exif.getAttributeInt(ExifInterface.TAG_ORIENTATION, 1).toString(),
                "Flash" to (exif.getAttribute(ExifInterface.TAG_FLASH) ?: ""),
                "FocalLength" to (exif.getAttribute(ExifInterface.TAG_FOCAL_LENGTH) ?: ""),
                "ISO" to (exif.getAttribute(ExifInterface.TAG_ISO_SPEED_RATINGS) ?: ""),
                "ExposureTime" to (exif.getAttribute(ExifInterface.TAG_EXPOSURE_TIME) ?: ""),
                "FNumber" to (exif.getAttribute(ExifInterface.TAG_F_NUMBER) ?: ""),
                "WhiteBalance" to (exif.getAttribute(ExifInterface.TAG_WHITE_BALANCE) ?: ""),
                "GPS" to "${exif.getAttribute(ExifInterface.TAG_GPS_LATITUDE) ?: ""},${exif.getAttribute(ExifInterface.TAG_GPS_LONGITUDE) ?: ""}"
            ).filterValues { it.isNotEmpty() }
        } catch (e: Exception) {
            emptyMap()
        }
    }

    /**
     * Extract dominant colors from image using Palette API
     */
    private suspend fun extractColorPalette(bitmap: Bitmap): List<String> = withContext(Dispatchers.Default) {
        try {
            val palette = Palette.from(bitmap).generate()
            listOfNotNull(
                palette.dominantSwatch?.rgb?.let { "#${Integer.toHexString(it).substring(2)}" },
                palette.vibrantSwatch?.rgb?.let { "#${Integer.toHexString(it).substring(2)}" },
                palette.mutedSwatch?.rgb?.let { "#${Integer.toHexString(it).substring(2)}" },
                palette.lightVibrantSwatch?.rgb?.let { "#${Integer.toHexString(it).substring(2)}" },
                palette.darkVibrantSwatch?.rgb?.let { "#${Integer.toHexString(it).substring(2)}" }
            ).distinct().take(5)
        } catch (e: Exception) {
            emptyList()
        }
    }

    /**
     * Check if image has transparency
     */
    private fun hasTransparency(bitmap: Bitmap): Boolean {
        return bitmap.hasAlpha() && bitmap.config == Bitmap.Config.ARGB_8888
    }

    /**
     * Correct image orientation based on EXIF data
     */
    private fun correctImageOrientation(bitmap: Bitmap, orientation: Int): Bitmap {
        val matrix = Matrix()
        when (orientation) {
            ExifInterface.ORIENTATION_ROTATE_90 -> matrix.postRotate(90f)
            ExifInterface.ORIENTATION_ROTATE_180 -> matrix.postRotate(180f)
            ExifInterface.ORIENTATION_ROTATE_270 -> matrix.postRotate(270f)
            ExifInterface.ORIENTATION_FLIP_HORIZONTAL -> matrix.postScale(-1f, 1f)
            ExifInterface.ORIENTATION_FLIP_VERTICAL -> matrix.postScale(1f, -1f)
            else -> return bitmap
        }
        
        return Bitmap.createBitmap(bitmap, 0, 0, bitmap.width, bitmap.height, matrix, true)
    }

    /**
     * Compress image to specified maximum size while maintaining aspect ratio
     */
    private fun compressImage(bitmap: Bitmap, maxSize: Int): Bitmap {
        val width = bitmap.width
        val height = bitmap.height
        
        if (width <= maxSize && height <= maxSize) {
            return bitmap
        }
        
        val ratio = min(maxSize.toFloat() / width, maxSize.toFloat() / height)
        val newWidth = (width * ratio).toInt()
        val newHeight = (height * ratio).toInt()
        
        return Bitmap.createScaledBitmap(bitmap, newWidth, newHeight, true)
    }

    /**
     * Save bitmap to file with specified quality
     */
    private fun saveImageToFile(bitmap: Bitmap, file: File, quality: Int) {
        FileOutputStream(file).use { out ->
            bitmap.compress(Bitmap.CompressFormat.JPEG, quality, out)
        }
    }

    /**
     * Save original file without processing
     */
    private fun saveOriginalToFile(context: Context, uri: Uri, file: File) {
        context.contentResolver.openInputStream(uri)?.use { input ->
            FileOutputStream(file).use { output ->
                input.copyTo(output)
            }
        }
    }

    /**
     * Check if image needs compression
     */
    private fun needsCompression(bitmap: Bitmap): Boolean {
        return bitmap.width > MAX_IMAGE_SIZE || bitmap.height > MAX_IMAGE_SIZE
    }

    /**
     * Get file size from URI
     */
    private fun getFileSize(context: Context, uri: Uri): Long {
        return try {
            context.contentResolver.openInputStream(uri)?.use { it.available().toLong() } ?: 0L
        } catch (e: Exception) {
            0L
        }
    }

    /**
     * Create temporary file
     */
    private fun createTempFile(context: Context, prefix: String, extension: String): File {
        val timestamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
        return File(context.cacheDir, "${prefix}_${timestamp}.$extension")
    }

    /**
     * Get file extension from filename
     */
    private fun getFileExtension(filename: String): String {
        return filename.substringAfterLast('.', "jpg")
    }

    /**
     * Get MIME type from filename
     */
    fun getMimeType(filename: String): String {
        val extension = getFileExtension(filename)
        return MimeTypeMap.getSingleton().getMimeTypeFromExtension(extension) ?: "image/jpeg"
    }

    /**
     * Generate unique filename
     */
    fun generateUniqueFilename(originalFilename: String): String {
        val timestamp = System.currentTimeMillis()
        val extension = getFileExtension(originalFilename)
        val nameWithoutExt = originalFilename.substringBeforeLast('.')
        return "${nameWithoutExt}_${timestamp}.$extension"
    }

    /**
     * Validate image file
     */
    fun isValidImageFile(filename: String): Boolean {
        val extension = getFileExtension(filename).lowercase()
        return extension in listOf("jpg", "jpeg", "png", "webp", "gif")
    }

    /**
     * Get image format from filename
     */
    fun getImageFormat(filename: String): String {
        return when (getFileExtension(filename).lowercase()) {
            "jpg", "jpeg" -> "JPEG"
            "png" -> "PNG"
            "webp" -> "WEBP"
            "gif" -> "GIF"
            else -> "UNKNOWN"
        }
    }
}

/**
 * Data class for processed image results
 */
data class ProcessedImageData(
    val originalFile: File,
    val compressedFile: File,
    val thumbnailFile: File,
    val metadata: ImageMetadata,
    val width: Int,
    val height: Int,
    val fileSize: Long
)
