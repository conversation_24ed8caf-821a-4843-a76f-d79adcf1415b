package com.tfkcolin.joceladmin.services

import android.content.Context
import android.net.Uri
import com.google.firebase.storage.FirebaseStorage
import com.google.firebase.storage.StorageReference
import com.google.firebase.storage.UploadTask
import com.tfkcolin.joceladmin.data.models.ImageData
import com.tfkcolin.joceladmin.data.models.ImageUploadRequest
import com.tfkcolin.joceladmin.repository.ImageDataRepository
import com.tfkcolin.joceladmin.utils.ImageUtils
import com.tfkcolin.joceladmin.utils.ProcessedImageData
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.tasks.await
import kotlinx.coroutines.withContext
import java.io.File
import javax.inject.Inject
import javax.inject.Named
import javax.inject.Singleton

/**
 * Service for handling image upload operations to Firebase Storage
 */
@Singleton
class ImageUploadService @Inject constructor(
    @ApplicationContext private val context: Context,
    private val storage: FirebaseStorage,
    @Named("IMAGES_STORAGE") private val imagesStorage: StorageReference,
    @Named("THUMBNAILS_STORAGE") private val thumbnailsStorage: StorageReference,
    @Named("COMPRESSED_STORAGE") private val compressedStorage: StorageReference,
    private val imageDataRepository: ImageDataRepository
) {

    /**
     * Upload image with progress tracking
     */
    fun uploadImage(
        uri: Uri,
        request: ImageUploadRequest
    ): Flow<ImageUploadProgress> = flow {
        try {
            emit(ImageUploadProgress.Processing("Validating image..."))

            // Validate image
            if (!ImageUtils.isValidImageFile(request.filename)) {
                throw IllegalArgumentException("Invalid image file format")
            }

            emit(ImageUploadProgress.Processing("Processing image..."))

            // Process image (extract metadata, create thumbnails, compress)
            val processedData = ImageUtils.processImage(context, uri, request.filename)
                .getOrThrow()

            emit(ImageUploadProgress.Processing("Creating database entry..."))

            // Create database entry first
            val imageData = imageDataRepository.createImage(request).getOrThrow()

            emit(ImageUploadProgress.Processing("Uploading original image..."))

            // Upload original image
            val originalUrl = uploadFile(
                file = processedData.originalFile,
                path = "original/${imageData.id}/${request.filename}",
                storageRef = imagesStorage
            ) { progress ->
                emit(ImageUploadProgress.Uploading("original", progress))
            }

            emit(ImageUploadProgress.Processing("Uploading compressed image..."))

            // Upload compressed image
            val compressedUrl = uploadFile(
                file = processedData.compressedFile,
                path = "compressed/${imageData.id}/${request.filename}",
                storageRef = compressedStorage
            ) { progress ->
                emit(ImageUploadProgress.Uploading("compressed", progress))
            }

            emit(ImageUploadProgress.Processing("Uploading thumbnail..."))

            // Upload thumbnail
            val thumbnailUrl = uploadFile(
                file = processedData.thumbnailFile,
                path = "thumbnails/${imageData.id}/${request.filename}",
                storageRef = thumbnailsStorage
            ) { progress ->
                emit(ImageUploadProgress.Uploading("thumbnail", progress))
            }

            emit(ImageUploadProgress.Processing("Updating database..."))

            // Update image data with URLs and metadata
            val updatedImageData = imageDataRepository.updateImageUrls(
                id = imageData.id,
                originalUrl = originalUrl,
                thumbnailUrl = thumbnailUrl,
                compressedUrl = compressedUrl,
                metadata = processedData.metadata
            ).getOrThrow()

            // Clean up temporary files
            cleanupTempFiles(processedData)

            emit(ImageUploadProgress.Success(updatedImageData))

        } catch (e: Exception) {
            emit(ImageUploadProgress.Error(e.message ?: "Upload failed"))
        }
    }.flowOn(Dispatchers.IO)

    /**
     * Upload multiple images
     */
    fun uploadMultipleImages(
        imageRequests: List<Pair<Uri, ImageUploadRequest>>
    ): Flow<MultipleImageUploadProgress> = flow {
        val totalImages = imageRequests.size
        var completedImages = 0
        val results = mutableListOf<ImageUploadResult>()

        emit(MultipleImageUploadProgress.Started(totalImages))

        imageRequests.forEachIndexed { index, (uri, request) ->
            emit(MultipleImageUploadProgress.ProcessingImage(index + 1, request.filename))

            try {
                uploadImage(uri, request).collect { progress ->
                    when (progress) {
                        is ImageUploadProgress.Success -> {
                            completedImages++
                            results.add(ImageUploadResult.Success(progress.imageData))
                            emit(MultipleImageUploadProgress.ImageCompleted(
                                completedImages, 
                                totalImages, 
                                request.filename
                            ))
                        }
                        is ImageUploadProgress.Error -> {
                            completedImages++
                            results.add(ImageUploadResult.Error(request.filename, progress.message))
                            emit(MultipleImageUploadProgress.ImageFailed(
                                completedImages, 
                                totalImages, 
                                request.filename, 
                                progress.message
                            ))
                        }
                        else -> {
                            // Forward individual progress
                            emit(MultipleImageUploadProgress.IndividualProgress(
                                index + 1, 
                                request.filename, 
                                progress
                            ))
                        }
                    }
                }
            } catch (e: Exception) {
                completedImages++
                results.add(ImageUploadResult.Error(request.filename, e.message ?: "Unknown error"))
                emit(MultipleImageUploadProgress.ImageFailed(
                    completedImages, 
                    totalImages, 
                    request.filename, 
                    e.message ?: "Unknown error"
                ))
            }
        }

        emit(MultipleImageUploadProgress.Completed(results))
    }.flowOn(Dispatchers.IO)

    /**
     * Delete image and its files
     */
    suspend fun deleteImage(imageData: ImageData): Result<Boolean> = withContext(Dispatchers.IO) {
        try {
            // Delete files from storage
            deleteImageFiles(imageData)

            // Delete from database
            imageDataRepository.deleteImage(imageData.id).getOrThrow()

            Result.success(true)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Upload file to Firebase Storage with progress tracking
     */
    private suspend fun uploadFile(
        file: File,
        path: String,
        storageRef: StorageReference,
        onProgress: suspend (Int) -> Unit = {}
    ): String = withContext(Dispatchers.IO) {
        val fileRef = storageRef.child(path)
        val uploadTask = fileRef.putFile(Uri.fromFile(file))

        // Track upload progress
        uploadTask.addOnProgressListener { taskSnapshot ->
            val progress = (100.0 * taskSnapshot.bytesTransferred / taskSnapshot.totalByteCount).toInt()
            // Note: This runs on main thread, but we'll handle it in the flow
        }

        // Wait for upload to complete
        uploadTask.await()

        // Get download URL
        fileRef.downloadUrl.await().toString()
    }

    /**
     * Delete image files from storage
     */
    private suspend fun deleteImageFiles(imageData: ImageData) = withContext(Dispatchers.IO) {
        try {
            // Delete original
            if (imageData.originalUrl.isNotEmpty()) {
                storage.getReferenceFromUrl(imageData.originalUrl).delete().await()
            }

            // Delete compressed
            if (imageData.compressedUrl.isNotEmpty()) {
                storage.getReferenceFromUrl(imageData.compressedUrl).delete().await()
            }

            // Delete thumbnail
            if (imageData.thumbnailUrl.isNotEmpty()) {
                storage.getReferenceFromUrl(imageData.thumbnailUrl).delete().await()
            }
        } catch (e: Exception) {
            // Log error but don't fail the operation
            println("Failed to delete some image files: ${e.message}")
        }
    }

    /**
     * Clean up temporary files
     */
    private fun cleanupTempFiles(processedData: ProcessedImageData) {
        try {
            processedData.originalFile.delete()
            processedData.compressedFile.delete()
            processedData.thumbnailFile.delete()
        } catch (e: Exception) {
            // Log error but don't fail
            println("Failed to cleanup temp files: ${e.message}")
        }
    }
}

/**
 * Progress states for single image upload
 */
sealed class ImageUploadProgress {
    data class Processing(val message: String) : ImageUploadProgress()
    data class Uploading(val type: String, val progress: Int) : ImageUploadProgress()
    data class Success(val imageData: ImageData) : ImageUploadProgress()
    data class Error(val message: String) : ImageUploadProgress()
}

/**
 * Progress states for multiple image upload
 */
sealed class MultipleImageUploadProgress {
    data class Started(val totalImages: Int) : MultipleImageUploadProgress()
    data class ProcessingImage(val currentIndex: Int, val filename: String) : MultipleImageUploadProgress()
    data class IndividualProgress(val imageIndex: Int, val filename: String, val progress: ImageUploadProgress) : MultipleImageUploadProgress()
    data class ImageCompleted(val completed: Int, val total: Int, val filename: String) : MultipleImageUploadProgress()
    data class ImageFailed(val completed: Int, val total: Int, val filename: String, val error: String) : MultipleImageUploadProgress()
    data class Completed(val results: List<ImageUploadResult>) : MultipleImageUploadProgress()
}

/**
 * Result for individual image upload in batch
 */
sealed class ImageUploadResult {
    data class Success(val imageData: ImageData) : ImageUploadResult()
    data class Error(val filename: String, val message: String) : ImageUploadResult()
}
